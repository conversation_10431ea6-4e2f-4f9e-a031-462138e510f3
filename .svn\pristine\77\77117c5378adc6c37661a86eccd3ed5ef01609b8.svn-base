import projectLogo from '@/static/logo.png'

export const isDev = process.env.NODE_ENV == 'development'

// 安全协议
export const protocolSecure = isDev ? false : true

// 域名
export const domain = isDev ? '192.168.2.45:8088' : 'nk.zrzyfw.cn:8088'

// 请求基础路径
export const baseUrl = isDev ? `http${protocolSecure ? 's' : ''}://${domain}` : '/prod-api'

// 静态资源访问路径
export const staticBaseUrl = isDev ? `http${protocolSecure ? 's' : ''}://${domain}` : '/prod-api'

// webSokcet 连接路径
export const socketUrl = isDev ? `ws${protocolSecure ? 's' : ''}://${domain}/api/socket?token=` : `ws${protocolSecure ? 's' : ''}://${domain}/api/socket?token=`

// 文件上传路径
export const uploadUrl = baseUrl + '/nk/enrollreceipt/upload'

// 请求超时时间
export const requestTimeout = 1000 * 10

// 上传文件超时时间
export const uploadTimeout = 1000 * 60

// 项目 logo
export const logo = projectLogo

// 系统信息
export const sysInfo = uni.getSystemInfoSync()

// 版本号
export const appVersion = sysInfo.appVersion

const { platform, uniPlatform } = sysInfo

export const isIos = platform == "ios"

export const isIosApp = platform == "ios" && uniPlatform == 'app'