{"name": "活动报名", "appid": "__UNI__15620A6", "description": "中心所有活动报名界面管理", "versionName": "1.0.0", "versionCode": "100", "transformPx": false, "app-plus": {"usingComponents": true, "nvueStyleCompiler": "uni-app", "compilerVersion": 3, "splashscreen": {"alwaysShowBeforeRender": false, "waiting": true, "autoclose": true, "delay": 0}, "modules": {"Camera": {}}, "distribute": {"android": {"permissions": ["<uses-feature android:name=\"android.hardware.camera\"/>", "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>", "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>", "<uses-permission android:name=\"android.permission.VIBRATE\"/>", "<uses-permission android:name=\"android.permission.READ_LOGS\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.CAMERA\"/>", "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>", "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>", "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>", "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>", "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>"], "abiFilters": ["armeabi-v7a", "arm64-v8a", "x86"], "targetSdkVersion": 31, "minSdkVersion": 21}, "ios": {"dSYMs": false, "privacyDescription": {"NSMicrophoneUsageDescription": "发送语音消息", "NSPhotoLibraryUsageDescription": "发送图片, 修改用户头像", "NSPhotoLibraryAddUsageDescription": "保存图片", "NSCameraUsageDescription": "发送图片, 修改用户头像"}}, "sdkConfigs": {"ad": {}}, "icons": {}}}, "quickapp": {}, "mp-weixin": {"appid": "", "setting": {"urlCheck": false}, "usingComponents": true, "requiredPrivateInfos": ["getLocation", "chooseLocation"]}, "mp-alipay": {"usingComponents": true}, "mp-baidu": {"usingComponents": true}, "mp-toutiao": {"usingComponents": true}, "uniStatistics": {"enable": false}, "vueVersion": "3", "h5": {"devServer": {"port": 8000}, "router": {"mode": "hash", "base": "./"}, "optimization": {"treeShaking": {"enable": true}}}, "locale": "zh-Hans", "fallbackLocale": "zh-Hans"}