<template>
	<fr-svg :width="size" :height="size" :src="iconSvg" />
</template>
<script>
	// #ifdef UNI-APP-X
	import icons from "../../mixins/icons.uts";
	// #endif
	// #ifndef UNI-APP-X
	import icons from "../../mixins/icons.js";
	// #endif
	export default {
		mixins: [icons],
		computed: {
			// #ifdef UNI-APP-X
			iconSvg(): string {
			// #endif
			// #ifndef UNI-APP-X
			iconSvg() {
			// #endif
				return `<?xml version="1.0" encoding="UTF-8"?><svg width="${this.size}" height="${this.size}" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#icon-499ebff9a020f4f)"><path d="M17.1182 26.0132L9.25293 12.3902C8.80961 11.6224 7.82778 11.3593 7.05994 11.8026C6.38243 12.1938 6.08553 13.0164 6.35702 13.7501L11.8773 28.6684C11.9585 28.8878 12.0005 29.1197 12.0016 29.3535L12.0586 42.0265C12.0636 43.1276 12.9575 44.0175 14.0586 44.0175L19.0159 44.0175C20.2925 44.0175 21.2424 42.8377 20.9698 41.5906L18 28L17.1182 26.0132Z" stroke="${this.colors(0)}" stroke-width="${this.strokeWidth}" stroke-linecap="${this.strokeLinecap}" stroke-linejoin="${this.strokeLinejoin}"/><path d="M12 29L18 28" stroke="${this.colors(0)}" stroke-width="${this.strokeWidth}" stroke-linecap="${this.strokeLinecap}" stroke-linejoin="${this.strokeLinejoin}"/><path d="M30.5636 25.7289L38.2548 12.4074C38.7036 11.6301 39.6975 11.3637 40.4748 11.8125C41.1469 12.2006 41.4508 13.0097 41.2005 13.7443L36.0954 28.7215C36.0325 28.9062 35.9969 29.099 35.9898 29.294L35.5236 42.0923C35.4844 43.1678 34.6012 44.0195 33.5249 44.0195L28.5607 44.0195C27.2598 44.0195 26.3051 42.7971 26.6203 41.5349L30.0005 28L30.3348 26.3351C30.3776 26.122 30.4549 25.9172 30.5636 25.7289Z" stroke="${this.colors(0)}" stroke-width="${this.strokeWidth}" stroke-linecap="${this.strokeLinecap}" stroke-linejoin="${this.strokeLinejoin}"/><path d="M30 28L36 29" stroke="${this.colors(0)}" stroke-width="${this.strokeWidth}" stroke-linecap="${this.strokeLinecap}" stroke-linejoin="${this.strokeLinejoin}"/><path d="M15 7L18.5 11" stroke="${this.colors(0)}" stroke-width="${this.strokeWidth}" stroke-linecap="${this.strokeLinecap}" stroke-linejoin="${this.strokeLinejoin}"/><path d="M23 4L24 10" stroke="${this.colors(0)}" stroke-width="${this.strokeWidth}" stroke-linecap="${this.strokeLinecap}" stroke-linejoin="${this.strokeLinejoin}"/><path d="M33 6L30 10" stroke="${this.colors(0)}" stroke-width="${this.strokeWidth}" stroke-linecap="${this.strokeLinecap}" stroke-linejoin="${this.strokeLinejoin}"/></g><defs><clipPath id="icon-499ebff9a020f4f"><rect width="48" height="48" fill="${this.colors(2)}"/></clipPath></defs></svg>`
			}
		}
	}
</script>