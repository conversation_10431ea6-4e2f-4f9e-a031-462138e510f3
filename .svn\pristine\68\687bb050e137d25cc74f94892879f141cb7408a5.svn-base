import{_ as e}from"./x-placeholder.bJd8l7m4.js";import{E as l,G as a,A as s,r as o,j as r,c as t,w as c,o as d,k as n,_ as u,d as p,t as m,S as f,T as i,U as h,i as _,f as b,I as y,g}from"./index-BGIsynos.js";import{_ as w,a as V}from"./my-container.CIY3D98s.js";import"./com.lqmVd1lZ.js";const x=w({__name:"register",setup(w){const{gProps:x,userStore:v,globalStore:N}=l(),j=a({phoneNumber:"",code:"",password:"",rePassword:""});function k(){const e=x.hasEmptyField(j);if(e)return x.$toast({phoneNumber:"请填写手机号",code:"请填写验证码",password:"请填写密码",rePassword:"请填写确认密码"}[e]);f(j).then((e=>{x.$navTo("/pages/login")}))}let P;const U=s("发送验证码");function $(){return P?x.$toast("请稍后再试"):j.phoneNumber?void i({phoneNumber:j.phoneNumber}).then((e=>{P=h(Date.now()+12e4,(({str:e},l)=>{U.value=e,l&&(P=null,U.value="发送验证码")}),"s S")})):x.$toast("请输入手机号")}return(l,a)=>{const s=o(r("x-placeholder"),e),f=_,i=b,h=y,w=g,x=o(r("my-container"),V);return d(),t(x,null,{default:c((()=>[n(w,{class:"container f-c-x-c"},{default:c((()=>[n(s,{height:44}),n(f,{class:"logo",src:u,mode:""}),n(w,{class:"ipt"},{default:c((()=>[n(i,null,{default:c((()=>[p("手机号")])),_:1}),n(w,{class:"f-r-y-c"},{default:c((()=>[n(h,{modelValue:j.phoneNumber,"onUpdate:modelValue":a[0]||(a[0]=e=>j.phoneNumber=e),class:"f-1",type:"number",placeholder:"输入手机号","placeholder-class":"placeholder-class"},null,8,["modelValue"])])),_:1})])),_:1}),n(w,{class:"ipt",style:{"margin-bottom":"20rpx"}},{default:c((()=>[n(i,null,{default:c((()=>[p("验证码")])),_:1}),n(w,{class:"f-r-y-c"},{default:c((()=>[n(h,{modelValue:j.code,"onUpdate:modelValue":a[1]||(a[1]=e=>j.code=e),class:"f-1",type:"number",placeholder:"输入验证码","placeholder-class":"placeholder-class"},null,8,["modelValue"]),n(i,{style:{"font-size":"25rpx",color:"var(--theme-color)"},onClick:$},{default:c((()=>[p(m(U.value),1)])),_:1})])),_:1})])),_:1}),n(w,{class:"ipt",style:{"margin-bottom":"20rpx"}},{default:c((()=>[n(i,null,{default:c((()=>[p("密码")])),_:1}),n(w,{class:"f-r-y-c"},{default:c((()=>[n(h,{modelValue:j.password,"onUpdate:modelValue":a[2]||(a[2]=e=>j.password=e),class:"f-1",type:"password",placeholder:"输入密码","placeholder-class":"placeholder-class"},null,8,["modelValue"])])),_:1})])),_:1}),n(w,{class:"ipt",style:{"margin-bottom":"20rpx"}},{default:c((()=>[n(i,null,{default:c((()=>[p("确认密码")])),_:1}),n(w,{class:"f-r-y-c"},{default:c((()=>[n(h,{modelValue:j.rePassword,"onUpdate:modelValue":a[3]||(a[3]=e=>j.rePassword=e),class:"f-1",type:"password",placeholder:"再次输入密码","placeholder-class":"placeholder-class"},null,8,["modelValue"])])),_:1})])),_:1}),n(w,{class:"btn",style:{"margin-top":"70rpx"},onClick:k},{default:c((()=>[p(" 确认 ")])),_:1}),n(w,{class:"btn",style:{"background-color":"transparent",color:"var(--theme-color)"},onClick:a[4]||(a[4]=e=>l.$redTo("/pages/login"))},{default:c((()=>[p(" 已有账户? ")])),_:1})])),_:1})])),_:1})}}},[["__scopeId","data-v-77345dec"]]);export{x as default};
