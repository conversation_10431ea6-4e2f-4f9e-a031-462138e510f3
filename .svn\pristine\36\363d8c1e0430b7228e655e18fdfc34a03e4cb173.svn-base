<template>
	<fr-svg :width="size" :height="size" :src="iconSvg" />
</template>
<script>
	// #ifdef UNI-APP-X
	import icons from "../../mixins/icons.uts";
	// #endif
	// #ifndef UNI-APP-X
	import icons from "../../mixins/icons.js";
	// #endif
	export default {
		mixins: [icons],
		computed: {
			// #ifdef UNI-APP-X
			iconSvg(): string {
			// #endif
			// #ifndef UNI-APP-X
			iconSvg() {
			// #endif
				return `<?xml version="1.0" encoding="UTF-8"?><svg width="${this.size}" height="${this.size}" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M23.5 20.4998C22.3462 20.4998 19.1521 20.3312 15.1138 24.9463C12.7115 27.6919 12.2869 31.0329 12.4644 33.3563C12.5487 34.4608 13.6689 35.0696 14.6977 34.659C15.4421 34.3619 16.2574 33.9912 16.8445 33.5998C18.5752 32.446 17.9983 31.2922 18.5752 29.5615C19.1521 27.8308 21.4597 26.677 23.7673 27.2539C26.0748 27.8308 26.957 29.583 27.2286 31.2922C27.5 32.9999 26.5 34.9999 24.3442 35.9074C21.9663 36.9084 20.8828 35.9074 17.9983 35.3305C16.4951 35.0299 14.992 36.3448 14.1267 37.2981C13.6367 37.8379 13.5842 38.6235 14.0092 39.2157C14.2979 39.6179 14.6703 40.0792 15.1138 40.5226C16.2676 41.6764 20.8828 45.1378 25.498 44.5609C30.1131 43.984 34.1514 40.5226 35.8821 36.4843C37.6128 32.446 37 26.9999 33 23.9999" stroke="${this.colors(0)}" stroke-width="${this.strokeWidth}" stroke-linecap="${this.strokeLinecap}" stroke-linejoin="${this.strokeLinejoin}"/><path d="M33.0005 23.9998L23.1283 4.24016C22.8984 3.78013 22.3524 3.56306 21.9065 3.81927C20.8655 4.41745 19.6474 5.54195 20.0432 7.28675C20.5537 9.53717 25.5812 27.0483 27 29.9998" stroke="${this.colors(0)}" stroke-width="${this.strokeWidth}" stroke-linecap="${this.strokeLinecap}" stroke-linejoin="${this.strokeLinejoin}"/><path d="M36 27L35.0522 7.69008C35.0277 7.19096 34.637 6.77864 34.1383 6.81033C32.9368 6.88668 31.3235 7.35813 30.913 9.11686C30.3885 11.3641 30.0139 14.7253 30 18" stroke="${this.colors(0)}" stroke-width="${this.strokeWidth}" stroke-linecap="${this.strokeLinecap}" stroke-linejoin="${this.strokeLinejoin}"/></svg>`
			}
		}
	}
</script>