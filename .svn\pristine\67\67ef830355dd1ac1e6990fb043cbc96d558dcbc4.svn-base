<template>
    <view class="orbit-spinner" :style="spinnerStyle">
        <view class="orbit one" :style="orbitStyle" />
        <view class="orbit two" :style="orbitStyle" />
        <view class="orbit three" :style="orbitStyle" />
    </view>
</template>

<script>
    export default {
        name: 'OrbitSpinner',

        props: {
            animationDuration: {
                type: Number,
                default: 1000,
            },
            size: {
                type: Number,
                default: 50,
            },
            color: {
                type: String,
                default: 'skyblue',
            },
        },

        computed: {
            spinnerStyle() {
                return {
                    height: `${this.size}px`,
                    width: `${this.size}px`,
                }
            },

            orbitStyle() {
                return {
                    borderColor: this.color,
                    animationDuration: `${this.animationDuration}ms`,
                }
            },
        },
    }
</script>

<style lang="css">
    view {
        box-sizing: border-box;
    }

    .orbit-spinner {
        height: 55px;
        width: 55px;
        border-radius: 50%;
        perspective: 800px;
    }

    .orbit-spinner .orbit {
        position: absolute;
        box-sizing: border-box;
        width: 100%;
        height: 100%;
        border-radius: 50%;
    }

    .orbit-spinner .orbit:nth-child(1) {
        left: 0%;
        top: 0%;
        animation: orbit-spinner-orbit-one-animation 1200ms linear infinite;
        border-bottom: 3px solid skyblue;
    }

    .orbit-spinner .orbit:nth-child(2) {
        right: 0%;
        top: 0%;
        animation: orbit-spinner-orbit-two-animation 1200ms linear infinite;
        border-right: 3px solid skyblue;
    }

    .orbit-spinner .orbit:nth-child(3) {
        right: 0%;
        bottom: 0%;
        animation: orbit-spinner-orbit-three-animation 1200ms linear infinite;
        border-top: 3px solid skyblue;
    }

    @keyframes orbit-spinner-orbit-one-animation {
        0% {
            transform: rotateX(35deg) rotateY(-45deg) rotateZ(0deg);
        }

        100% {
            transform: rotateX(35deg) rotateY(-45deg) rotateZ(360deg);
        }
    }

    @keyframes orbit-spinner-orbit-two-animation {
        0% {
            transform: rotateX(50deg) rotateY(10deg) rotateZ(0deg);
        }

        100% {
            transform: rotateX(50deg) rotateY(10deg) rotateZ(360deg);
        }
    }

    @keyframes orbit-spinner-orbit-three-animation {
        0% {
            transform: rotateX(35deg) rotateY(55deg) rotateZ(0deg);
        }

        100% {
            transform: rotateX(35deg) rotateY(55deg) rotateZ(360deg);
        }
    }
</style>