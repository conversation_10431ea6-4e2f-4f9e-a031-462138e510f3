{"id": "flower-icons", "displayName": "flower-icons 图标库，全平台全版本兼容，最新支持 uni-app-x", "version": "1.1.8", "description": "2600+基础图标，29种图标分类，通过技术驱动矢量图标样式的图标库产品，可以实现根据单一SVG源文件变换出多种主题，具备丰富的分类、更轻量的代码和更灵活的使用场景。", "keywords": ["iconPark,icon,图标库,vue,nvue,uvue,vue2版本,vue3版本"], "repository": "https://github.com/dengqichang/flower-library/tree/main/uni_modules/flower-icons", "engines": {"HBuilderX": "^3.99"}, "dcloudext": {"type": "component-vue", "sale": {"regular": {"price": "0.00"}, "sourcecode": {"price": "0.00"}}, "contact": {"qq": ""}, "declaration": {"ads": "无", "data": "插件不采集任何数据", "permissions": "无"}, "npmurl": ""}, "uni_modules": {"dependencies": ["flower-svg"], "encrypt": [], "platforms": {"cloud": {"tcb": "y", "aliyun": "y"}, "client": {"Vue": {"vue2": "y", "vue3": "y"}, "App": {"app-vue": "y", "app-nvue": "y", "app-uvue": "y"}, "H5-mobile": {"Safari": "y", "Android Browser": "y", "微信浏览器(Android)": "y", "QQ浏览器(Android)": "y"}, "H5-pc": {"Chrome": "y", "IE": "y", "Edge": "y", "Firefox": "y", "Safari": "y"}, "小程序": {"微信": "y", "阿里": "y", "百度": "y", "字节跳动": "y", "QQ": "y", "钉钉": "y", "快手": "y", "飞书": "y", "京东": "y"}, "快应用": {"华为": "y", "联盟": "y"}}}}}