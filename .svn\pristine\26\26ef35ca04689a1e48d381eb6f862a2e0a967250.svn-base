<template>
	<fr-svg :width="size" :height="size" :src="iconSvg" />
</template>
<script>
	// #ifdef UNI-APP-X
	import icons from "../../mixins/icons.uts";
	// #endif
	// #ifndef UNI-APP-X
	import icons from "../../mixins/icons.js";
	// #endif
	export default {
		mixins: [icons],
		computed: {
			// #ifdef UNI-APP-X
			iconSvg(): string {
			// #endif
			// #ifndef UNI-APP-X
			iconSvg() {
			// #endif
				return `<?xml version="1.0" encoding="UTF-8"?><svg width="${this.size}" height="${this.size}" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M20.9951 36L19.0937 31.9908L24.2032 31.5728C26.2913 31.5728 27.984 29.8795 27.984 27.7906C27.984 25.7017 26.2913 24.0083 24.2032 24.0083C20.4565 24.0083 17.7211 24.0083 15.9972 24.0083C12.6087 24.0083 9 27.584 9 31.0421C9 34.5003 9.78742 36.4809 10.6467 38.3387C11.506 40.1966 13.7968 44 20.0016 44C23.986 44 23.4272 44 28.9545 44C34.2029 44 38 39.1454 37.987 33C37.9789 29.1696 37.9814 23.5029 37.9944 16C37.9973 14.3482 36.6606 13.0068 35.0088 13.0039L35.0036 13.0039C33.3521 13.0039 32.0134 14.3427 32.0134 15.9942C32.0134 15.9962 32.0134 15.9982 32.0134 16.0002C32.0238 21.141 32.029 24.1579 32.029 25.051C32.029 27.9448 30.6885 28.9421 28.0074 28.0431M25.9546 24L25.9945 6.99998C25.9984 5.34703 24.6616 4.00389 23.0086 4.00001C23.0063 4 23.0039 4 23.0016 4C21.3447 4 20.0016 5.34314 20.0016 6.99999V23.0401" stroke="${this.colors(0)}" stroke-width="${this.strokeWidth}" stroke-linecap="${this.strokeLinecap}" stroke-linejoin="${this.strokeLinejoin}"/><path d="M14.0059 24.0002V10.0002C14.0059 8.34567 15.3471 7.00439 17.0017 7.00439C17.0031 7.00439 17.0045 7.0044 17.0059 7.0044C18.6634 7.0067 20.0059 8.35105 20.0059 10.0086V24.0002" stroke="${this.colors(0)}" stroke-width="${this.strokeWidth}" stroke-linecap="${this.strokeLinecap}" stroke-linejoin="${this.strokeLinejoin}"/><path d="M26.0073 20.0195V19.0195C26.0073 17.3627 27.3505 16.0195 29.0073 16.0195C30.6642 16.0195 32.0073 17.3627 32.0073 19.0195V20.0195" stroke="${this.colors(0)}" stroke-width="${this.strokeWidth}" stroke-linecap="${this.strokeLinecap}" stroke-linejoin="${this.strokeLinejoin}"/></svg>`
			}
		}
	}
</script>