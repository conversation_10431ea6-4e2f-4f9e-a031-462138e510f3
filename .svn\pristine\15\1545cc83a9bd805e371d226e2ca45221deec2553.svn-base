<template>
	<fr-svg :width="size" :height="size" :src="iconSvg" />
</template>
<script>
	// #ifdef UNI-APP-X
	import icons from "../../mixins/icons.uts";
	// #endif
	// #ifndef UNI-APP-X
	import icons from "../../mixins/icons.js";
	// #endif
	export default {
		mixins: [icons],
		computed: {
			// #ifdef UNI-APP-X
			iconSvg(): string {
			// #endif
			// #ifndef UNI-APP-X
			iconSvg() {
			// #endif
				return `<?xml version="1.0" encoding="UTF-8"?><svg width="${this.size}" height="${this.size}" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M13.0001 27C15.0727 26.4583 19.0142 23.8333 20.0001 23V16L14.0001 14C13.5893 12.3796 12.3146 9.11111 11.0001 8L9.55188 12.5139C6.9502 13.6713 2.69965 18.8889 5.00008 25C6.00008 27 8.077 34 11.0001 39" stroke="${this.colors(0)}" stroke-width="${this.strokeWidth}" stroke-linecap="${this.strokeLinecap}" stroke-linejoin="${this.strokeLinejoin}"/><path d="M24 13.2055C28.3911 12.479 37.5246 13.2781 38.9297 22.2864C39.2225 23.6184 38.7541 30.0088 34.5386 32.9147C33.6895 33.5 33 36 33 39" stroke="${this.colors(0)}" stroke-width="${this.strokeWidth}" stroke-linecap="${this.strokeLinecap}" stroke-linejoin="${this.strokeLinejoin}"/><path d="M26 40C26 37.7909 24.2091 36 22 36C19.7909 36 18 37.7909 18 40" stroke="${this.colors(0)}" stroke-width="${this.strokeWidth}" stroke-linecap="${this.strokeLinecap}"/><path d="M39 24C39.5 25 41.6985 25.6695 43.2277 24.1017C44.1179 23.189 44.8467 20.3339 42.598 19" stroke="${this.colors(0)}" stroke-width="${this.strokeWidth}" stroke-linecap="${this.strokeLinecap}" stroke-linejoin="${this.strokeLinejoin}"/></svg>`
			}
		}
	}
</script>