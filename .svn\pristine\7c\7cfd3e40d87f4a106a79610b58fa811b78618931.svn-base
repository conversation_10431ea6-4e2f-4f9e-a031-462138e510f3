<template>
	<fr-svg :width="size" :height="size" :src="iconSvg" />
</template>
<script>
	// #ifdef UNI-APP-X
	import icons from "../../mixins/icons.uts";
	// #endif
	// #ifndef UNI-APP-X
	import icons from "../../mixins/icons.js";
	// #endif
	export default {
		mixins: [icons],
		computed: {
			// #ifdef UNI-APP-X
			iconSvg(): string {
			// #endif
			// #ifndef UNI-APP-X
			iconSvg() {
			// #endif
				return `<?xml version="1.0" encoding="UTF-8"?><svg width="${this.size}" height="${this.size}" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><rect x="8" y="4" width="32" height="40" rx="2" stroke="${this.colors(0)}" stroke-width="${this.strokeWidth}" stroke-linecap="${this.strokeLinecap}" stroke-linejoin="${this.strokeLinejoin}"/><rect x="14" y="11" width="20" height="9" fill="${this.colors(1)}" stroke="${this.colors(0)}" stroke-width="${this.strokeWidth}" stroke-linecap="${this.strokeLinecap}" stroke-linejoin="${this.strokeLinejoin}"/><circle cx="17" cy="26" r="2" fill="${this.colors(0)}"/><circle cx="17" cy="32" r="2" fill="${this.colors(0)}"/><circle cx="17" cy="38" r="2" fill="${this.colors(0)}"/><circle cx="24" cy="26" r="2" fill="${this.colors(0)}"/><circle cx="24" cy="32" r="2" fill="${this.colors(0)}"/><circle cx="24" cy="38" r="2" fill="${this.colors(0)}"/><circle cx="31" cy="26" r="2" fill="${this.colors(0)}"/><circle cx="31" cy="32" r="2" fill="${this.colors(0)}"/><circle cx="31" cy="38" r="2" fill="${this.colors(0)}"/></svg>`
			}
		}
	}
</script>