<template>
	<fr-svg :width="size" :height="size" :src="iconSvg" />
</template>
<script>
	// #ifdef UNI-APP-X
	import icons from "../../mixins/icons.uts";
	// #endif
	// #ifndef UNI-APP-X
	import icons from "../../mixins/icons.js";
	// #endif
	export default {
		mixins: [icons],
		computed: {
			// #ifdef UNI-APP-X
			iconSvg(): string {
			// #endif
			// #ifndef UNI-APP-X
			iconSvg() {
			// #endif
				return `<?xml version="1.0" encoding="UTF-8"?><svg width="${this.size}" height="${this.size}" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M31 20V27L10 39L4 35V13L10 9L24 17L17.5 20.5L10 16V32L31 20Z" fill="${this.colors(1)}"/><path d="M31 27L31.9923 28.7365C32.6154 28.3804 33 27.7177 33 27H31ZM31 20H33V16.5536L30.0077 18.2635L31 20ZM10 32H8C8 32.7129 8.37952 33.372 8.99615 33.7298C9.61278 34.0877 10.3733 34.0902 10.9923 33.7365L10 32ZM10 16L11.029 14.285C10.4111 13.9143 9.64162 13.9046 9.0146 14.2596C8.38759 14.6146 8 15.2795 8 16H10ZM17.5 20.5L16.471 22.215C17.0759 22.5779 17.8271 22.5954 18.4482 22.2609L17.5 20.5ZM24 17L24.9482 18.7609C25.5881 18.4164 25.9907 17.7519 25.9998 17.0252C26.009 16.2985 25.6233 15.6241 24.9923 15.2635L24 17ZM10 9L10.9923 7.26351C10.3345 6.88767 9.52091 6.91569 8.8906 7.3359L10 9ZM4 13L2.8906 11.3359C2.3342 11.7068 2 12.3313 2 13H4ZM4 35H2C2 35.6687 2.3342 36.2932 2.8906 36.6641L4 35ZM10 39L8.8906 40.6641C9.52091 41.0843 10.3345 41.1123 10.9923 40.7365L10 39ZM33 27V20H29V27H33ZM30.0077 18.2635L9.00772 30.2635L10.9923 33.7365L31.9923 21.7365L30.0077 18.2635ZM12 32V16H8V32H12ZM8.97101 17.715L16.471 22.215L18.529 18.785L11.029 14.285L8.97101 17.715ZM18.4482 22.2609L24.9482 18.7609L23.0518 15.2391L16.5518 18.7391L18.4482 22.2609ZM24.9923 15.2635L10.9923 7.26351L9.00772 10.7365L23.0077 18.7365L24.9923 15.2635ZM8.8906 7.3359L2.8906 11.3359L5.1094 14.6641L11.1094 10.6641L8.8906 7.3359ZM2 13V35H6V13H2ZM2.8906 36.6641L8.8906 40.6641L11.1094 37.3359L5.1094 33.3359L2.8906 36.6641ZM10.9923 40.7365L31.9923 28.7365L30.0077 25.2635L9.00772 37.2635L10.9923 40.7365Z" fill="${this.colors(0)}"/><path d="M17 28V21L38 9L44 13V35L38 39L24 31L30.5 27.5L38 32V16L17 28Z" fill="${this.colors(1)}"/><path d="M17 21L16.0077 19.2635C15.3846 19.6196 15 20.2823 15 21H17ZM17 28H15V31.4464L17.9923 29.7365L17 28ZM38 16H40C40 15.2871 39.6205 14.628 39.0039 14.2702C38.3872 13.9123 37.6267 13.9098 37.0077 14.2635L38 16ZM38 32L36.971 33.715C37.5889 34.0857 38.3584 34.0954 38.9854 33.7404C39.6124 33.3854 40 32.7205 40 32H38ZM30.5 27.5L31.529 25.785C30.9241 25.4221 30.1729 25.4046 29.5518 25.7391L30.5 27.5ZM24 31L23.0518 29.2391C22.4119 29.5836 22.0093 30.2481 22.0002 30.9748C21.991 31.7015 22.3767 32.3759 23.0077 32.7365L24 31ZM38 39L37.0077 40.7365C37.6655 41.1123 38.4791 41.0843 39.1094 40.6641L38 39ZM44 35L45.1094 36.6641C45.6658 36.2932 46 35.6687 46 35H44ZM44 13H46C46 12.3313 45.6658 11.7068 45.1094 11.3359L44 13ZM38 9L39.1094 7.3359C38.4791 6.91569 37.6655 6.88766 37.0077 7.26351L38 9ZM15 21V28H19V21H15ZM17.9923 29.7365L38.9923 17.7365L37.0077 14.2635L16.0077 26.2635L17.9923 29.7365ZM36 16V32H40V16H36ZM39.029 30.285L31.529 25.785L29.471 29.215L36.971 33.715L39.029 30.285ZM29.5518 25.7391L23.0518 29.2391L24.9482 32.7609L31.4482 29.2609L29.5518 25.7391ZM23.0077 32.7365L37.0077 40.7365L38.9923 37.2635L24.9923 29.2635L23.0077 32.7365ZM39.1094 40.6641L45.1094 36.6641L42.8906 33.3359L36.8906 37.3359L39.1094 40.6641ZM46 35V13H42V35H46ZM45.1094 11.3359L39.1094 7.3359L36.8906 10.6641L42.8906 14.6641L45.1094 11.3359ZM37.0077 7.26351L16.0077 19.2635L17.9923 22.7365L38.9923 10.7365L37.0077 7.26351Z" fill="${this.colors(0)}"/></svg>`
			}
		}
	}
</script>