<template>
	<fr-svg :width="size" :height="size" :src="iconSvg" />
</template>
<script>
	// #ifdef UNI-APP-X
	import icons from "../../mixins/icons.uts";
	// #endif
	// #ifndef UNI-APP-X
	import icons from "../../mixins/icons.js";
	// #endif
	export default {
		mixins: [icons],
		computed: {
			// #ifdef UNI-APP-X
			iconSvg(): string {
			// #endif
			// #ifndef UNI-APP-X
			iconSvg() {
			// #endif
				return `<?xml version="1.0" encoding="UTF-8"?><svg width="${this.size}" height="${this.size}" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M12.5658 26.1824C10.8553 27.3542 10 29.2935 10 32.0002C10 36.0603 14.9753 43.0002 19.4618 43.0002C23.9483 43.0002 26.5502 43.0002 30.9415 43.0002C35.3328 43.0002 38 39.1496 38 36.0603C38 31.7099 38 27.3596 38 23.0093C38 21.3524 36.6569 20.0093 35 20.0093H34.9909C33.3391 20.0093 32 21.3484 32 23.0002" stroke="${this.colors(0)}" stroke-width="${this.strokeWidth}" stroke-linecap="${this.strokeLinecap}"/><path d="M13.981 28.4452V8.00587C13.981 6.35036 15.323 5.0083 16.9785 5.0083C16.9813 5.0083 16.9841 5.0083 16.9868 5.00831C18.6482 5.01291 19.9927 6.36106 19.9927 8.0225V23.592" stroke="${this.colors(0)}" stroke-width="${this.strokeWidth}" stroke-linecap="${this.strokeLinecap}"/><path d="M19.9927 23.0083V19.0155C19.9927 17.3501 21.3428 16 23.0082 16C24.6736 16 26.0236 17.3501 26.0236 19.0155V23.0083" stroke="${this.colors(0)}" stroke-width="${this.strokeWidth}" stroke-linecap="${this.strokeLinecap}" stroke-linejoin="${this.strokeLinejoin}"/><path d="M26 22.7158V20.0034C26 18.3466 27.3431 17.0034 29 17.0034C30.6569 17.0034 32 18.3466 32 20.0034V23.0034" stroke="${this.colors(0)}" stroke-width="${this.strokeWidth}" stroke-linecap="${this.strokeLinecap}" stroke-linejoin="${this.strokeLinejoin}"/></svg>`
			}
		}
	}
</script>