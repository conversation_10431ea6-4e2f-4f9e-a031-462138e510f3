var e,t,i,o,n,a,s,l,r,u,c,d,p,h,f,m,y,g,v,b,_,S,w,x,C,k;import{m as $,a as z,o as P,c as I,w as T,n as B,b as D,d as q,t as A,e as O,i as N,f as F,g as j,r as R,h as V,j as E,k as U,I as M,l as L,p as H,q as W,s as Y,u as X,F as K,v as Z,x as J,y as G,z as Q,A as ee,B as te,C as ie,D as oe,E as ne,G as ae,H as se,J as le,K as re,L as ue,M as ce,N as de,O as pe}from"./index-CYgRtUWM.js";import{_ as he,a as fe}from"./my-container.CD5dlYbT.js";const me={"uvicon-level":"e68f","uvicon-checkbox-mark":"e659","uvicon-folder":"e694","uvicon-movie":"e67c","uvicon-star-fill":"e61e","uvicon-star":"e618","uvicon-phone-fill":"e6ac","uvicon-phone":"e6ba","uvicon-apple-fill":"e635","uvicon-backspace":"e64d","uvicon-attach":"e640","uvicon-empty-data":"e671","uvicon-empty-address":"e68a","uvicon-empty-favor":"e662","uvicon-empty-car":"e657","uvicon-empty-order":"e66b","uvicon-empty-list":"e672","uvicon-empty-search":"e677","uvicon-empty-permission":"e67d","uvicon-empty-news":"e67e","uvicon-empty-history":"e685","uvicon-empty-coupon":"e69b","uvicon-empty-page":"e60e","uvicon-empty-wifi-off":"e6cc","uvicon-reload":"e627","uvicon-order":"e695","uvicon-server-man":"e601","uvicon-search":"e632","uvicon-more-dot-fill":"e66f","uvicon-scan":"e631","uvicon-map":"e665","uvicon-map-fill":"e6a8","uvicon-tags":"e621","uvicon-tags-fill":"e613","uvicon-eye":"e664","uvicon-eye-fill":"e697","uvicon-eye-off":"e69c","uvicon-eye-off-outline":"e688","uvicon-mic":"e66d","uvicon-mic-off":"e691","uvicon-calendar":"e65c","uvicon-trash":"e623","uvicon-trash-fill":"e6ce","uvicon-play-left":"e6bf","uvicon-play-right":"e6b3","uvicon-minus":"e614","uvicon-plus":"e625","uvicon-info-circle":"e69f","uvicon-info-circle-fill":"e6a7","uvicon-question-circle":"e622","uvicon-question-circle-fill":"e6bc","uvicon-close":"e65a","uvicon-checkmark":"e64a","uvicon-checkmark-circle":"e643","uvicon-checkmark-circle-fill":"e668","uvicon-setting":"e602","uvicon-setting-fill":"e6d0","uvicon-heart":"e6a2","uvicon-heart-fill":"e68b","uvicon-camera":"e642","uvicon-camera-fill":"e650","uvicon-more-circle":"e69e","uvicon-more-circle-fill":"e684","uvicon-chat":"e656","uvicon-chat-fill":"e63f","uvicon-bag":"e647","uvicon-error-circle":"e66e","uvicon-error-circle-fill":"e655","uvicon-close-circle":"e64e","uvicon-close-circle-fill":"e666","uvicon-share":"e629","uvicon-share-fill":"e6bb","uvicon-share-square":"e6c4","uvicon-shopping-cart":"e6cb","uvicon-shopping-cart-fill":"e630","uvicon-bell":"e651","uvicon-bell-fill":"e604","uvicon-list":"e690","uvicon-list-dot":"e6a9","uvicon-zhifubao-circle-fill":"e617","uvicon-weixin-circle-fill":"e6cd","uvicon-weixin-fill":"e620","uvicon-qq-fill":"e608","uvicon-qq-circle-fill":"e6b9","uvicon-moments-circel-fill":"e6c2","uvicon-moments":"e6a0","uvicon-car":"e64f","uvicon-car-fill":"e648","uvicon-warning-fill":"e6c7","uvicon-warning":"e6c1","uvicon-clock-fill":"e64b","uvicon-clock":"e66c","uvicon-edit-pen":"e65d","uvicon-edit-pen-fill":"e679","uvicon-email":"e673","uvicon-email-fill":"e683","uvicon-minus-circle":"e6a5","uvicon-plus-circle":"e603","uvicon-plus-circle-fill":"e611","uvicon-file-text":"e687","uvicon-file-text-fill":"e67f","uvicon-pushpin":"e6d1","uvicon-pushpin-fill":"e6b6","uvicon-grid":"e68c","uvicon-grid-fill":"e698","uvicon-play-circle":"e6af","uvicon-play-circle-fill":"e62a","uvicon-pause-circle-fill":"e60c","uvicon-pause":"e61c","uvicon-pause-circle":"e696","uvicon-gift-fill":"e6b0","uvicon-gift":"e680","uvicon-kefu-ermai":"e660","uvicon-server-fill":"e610","uvicon-coupon-fill":"e64c","uvicon-coupon":"e65f","uvicon-integral":"e693","uvicon-integral-fill":"e6b1","uvicon-home-fill":"e68e","uvicon-home":"e67b","uvicon-account":"e63a","uvicon-account-fill":"e653","uvicon-thumb-down-fill":"e628","uvicon-thumb-down":"e60a","uvicon-thumb-up":"e612","uvicon-thumb-up-fill":"e62c","uvicon-lock-fill":"e6a6","uvicon-lock-open":"e68d","uvicon-lock-opened-fill":"e6a1","uvicon-lock":"e69d","uvicon-red-packet":"e6c3","uvicon-photo-fill":"e6b4","uvicon-photo":"e60d","uvicon-volume-off-fill":"e6c8","uvicon-volume-off":"e6bd","uvicon-volume-fill":"e624","uvicon-volume":"e605","uvicon-download":"e670","uvicon-arrow-up-fill":"e636","uvicon-arrow-down-fill":"e638","uvicon-play-left-fill":"e6ae","uvicon-play-right-fill":"e6ad","uvicon-arrow-downward":"e634","uvicon-arrow-leftward":"e63b","uvicon-arrow-rightward":"e644","uvicon-arrow-upward":"e641","uvicon-arrow-down":"e63e","uvicon-arrow-right":"e63c","uvicon-arrow-left":"e646","uvicon-arrow-up":"e633","uvicon-skip-back-left":"e6c5","uvicon-skip-forward-right":"e61f","uvicon-arrow-left-double":"e637","uvicon-man":"e675","uvicon-woman":"e626","uvicon-en":"e6b8","uvicon-twitte":"e607","uvicon-twitter-circle-fill":"e6cf"};const ye=he({name:"uv-icon",emits:["click"],mixins:[$,z,{props:{name:{type:String,default:""},color:{type:String,default:"#606266"},size:{type:[String,Number],default:"16px"},bold:{type:Boolean,default:!1},index:{type:[String,Number],default:null},hoverClass:{type:String,default:""},customPrefix:{type:String,default:"uvicon"},label:{type:[String,Number],default:""},labelPos:{type:String,default:"right"},labelSize:{type:[String,Number],default:"15px"},labelColor:{type:String,default:"#606266"},space:{type:[String,Number],default:"3px"},imgMode:{type:String,default:"aspectFit"},width:{type:[String,Number],default:""},height:{type:[String,Number],default:""},top:{type:[String,Number],default:0},stop:{type:Boolean,default:!1},...uni.$uvCommonProps,...null==(t=null==(e=uni.$uv)?void 0:e.props)?void 0:t.icon}}],data:()=>({colorType:["primary","success","info","error","warning"]}),computed:{uClasses(){let e=[];return e.push(this.customPrefix),e.push(this.customPrefix+"-"+this.name),this.color&&this.colorType.includes(this.color)&&e.push("uv-icon__icon--"+this.color),e},iconStyle(){let e={};return e={fontSize:this.$uv.addUnit(this.size),lineHeight:this.$uv.addUnit(this.size),fontWeight:this.bold?"bold":"normal",top:this.$uv.addUnit(this.top)},this.color&&!this.colorType.includes(this.color)&&(e.color=this.color),e},isImg(){const e=this.name.indexOf("data:")>-1&&this.name.indexOf("base64")>-1;return-1!==this.name.indexOf("/")||e},imgStyle(){let e={};return e.width=this.width?this.$uv.addUnit(this.width):this.$uv.addUnit(this.size),e.height=this.height?this.$uv.addUnit(this.height):this.$uv.addUnit(this.size),e},icon(){const e=me["uvicon-"+this.name];return e?unescape(`%u${e}`):["uvicon"].indexOf(this.customPrefix)>-1?this.name:""}},methods:{clickHandler(e){this.$emit("click",this.index),this.stop&&this.preventEvent(e)}}},[["render",function(e,t,i,o,n,a){const s=N,l=F,r=j;return P(),I(r,{class:D(["uv-icon",["uv-icon--"+e.labelPos]]),onClick:a.clickHandler},{default:T((()=>[a.isImg?(P(),I(s,{key:0,class:"uv-icon__img",src:e.name,mode:e.imgMode,style:B([a.imgStyle,e.$uv.addStyle(e.customStyle)])},null,8,["src","mode","style"])):(P(),I(l,{key:1,class:D(["uv-icon__icon",a.uClasses]),style:B([a.iconStyle,e.$uv.addStyle(e.customStyle)]),"hover-class":e.hoverClass},{default:T((()=>[q(A(a.icon),1)])),_:1},8,["class","style","hover-class"])),""!==e.label?(P(),I(l,{key:2,class:"uv-icon__label",style:B({color:e.labelColor,fontSize:e.$uv.addUnit(e.labelSize),marginLeft:"right"==e.labelPos?e.$uv.addUnit(e.space):0,marginTop:"bottom"==e.labelPos?e.$uv.addUnit(e.space):0,marginRight:"left"==e.labelPos?e.$uv.addUnit(e.space):0,marginBottom:"top"==e.labelPos?e.$uv.addUnit(e.space):0})},{default:T((()=>[q(A(e.label),1)])),_:1},8,["style"])):O("",!0)])),_:1},8,["onClick","class"])}],["__scopeId","data-v-ae1d9482"]]);const ge=he({name:"uv-input",mixins:[$,z,{props:{value:{type:[String,Number],default:""},modelValue:{type:[String,Number],default:""},type:{type:String,default:"text"},disabled:{type:Boolean,default:!1},disabledColor:{type:String,default:"#f5f7fa"},clearable:{type:Boolean,default:!1},password:{type:Boolean,default:!1},maxlength:{type:[String,Number],default:-1},placeholder:{type:String,default:null},placeholderClass:{type:String,default:"input-placeholder"},placeholderStyle:{type:[String,Object],default:"color: #c0c4cc"},confirmType:{type:String,default:"done"},confirmHold:{type:Boolean,default:!1},holdKeyboard:{type:Boolean,default:!1},focus:{type:Boolean,default:!1},autoBlur:{type:Boolean,default:!1},cursor:{type:[String,Number],default:-1},cursorSpacing:{type:[String,Number],default:30},selectionStart:{type:[String,Number],default:-1},selectionEnd:{type:[String,Number],default:-1},adjustPosition:{type:Boolean,default:!0},inputAlign:{type:String,default:"left"},fontSize:{type:[String,Number],default:"14px"},color:{type:String,default:"#303133"},prefixIcon:{type:String,default:""},prefixIconStyle:{type:[String,Object],default:""},suffixIcon:{type:String,default:""},suffixIconStyle:{type:[String,Object],default:""},border:{type:String,default:"surround"},readonly:{type:Boolean,default:!1},shape:{type:String,default:"square"},formatter:{type:[Function,null],default:null},ignoreCompositionEvent:{type:Boolean,default:!0},...uni.$uvCommonProps,...null==(o=null==(i=uni.$uv)?void 0:i.props)?void 0:o.input}}],data:()=>({innerValue:"",focused:!1,innerFormatter:e=>e}),created(){this.innerValue=this.modelValue},watch:{value(e){this.innerValue=e},modelValue(e){this.innerValue=e}},computed:{isShowClear(){const{clearable:e,readonly:t,focused:i,innerValue:o}=this;return!!e&&!t&&!!i&&""!==o},inputClass(){let e=[],{border:t,disabled:i,shape:o}=this;return"surround"===t&&(e=e.concat(["uv-border","uv-input--radius"])),e.push(`uv-input--${o}`),"bottom"===t&&(e=e.concat(["uv-border-bottom","uv-input--no-radius"])),e.join(" ")},wrapperStyle(){const e={};return this.disabled&&(e.backgroundColor=this.disabledColor),"none"===this.border?e.padding="0":(e.paddingTop="6px",e.paddingBottom="6px",e.paddingLeft="9px",e.paddingRight="9px"),this.$uv.deepMerge(e,this.$uv.addStyle(this.customStyle))},inputStyle(){const e={color:this.color,fontSize:this.$uv.addUnit(this.fontSize),textAlign:this.inputAlign};return(this.disabled||this.readonly)&&(e["pointer-events"]="none"),e}},methods:{setFormatter(e){this.innerFormatter=e},onInput(e){let{value:t=""}=e.detail||{};const i=(this.formatter||this.innerFormatter)(t);this.innerValue=t,this.$nextTick((()=>{this.innerValue=i,this.valueChange()}))},onBlur(e){this.$emit("blur",e.detail.value),this.$uv.sleep(100).then((()=>{this.focused=!1})),this.$uv.formValidate(this,"blur")},onFocus(e){this.focused=!0,this.$emit("focus")},onConfirm(e){this.$emit("confirm",this.innerValue)},onkeyboardheightchange(e){this.$emit("keyboardheightchange",e)},valueChange(){this.isClear&&(this.innerValue="");const e=this.innerValue;this.$nextTick((()=>{this.$emit("input",e),this.$emit("update:modelValue",e),this.$emit("change",e),this.$uv.formValidate(this,"change")}))},onClear(){this.innerValue="",this.isClear=!0,this.$uv.sleep(100).then((e=>{this.isClear=!1})),this.$nextTick((()=>{this.$emit("clear"),this.valueChange()}))},clickHandler(){}}},[["render",function(e,t,i,o,n,a){const s=R(V("uv-icon"),ye),l=j,r=M;return P(),I(l,{class:D(["uv-input",a.inputClass]),style:B([a.wrapperStyle])},{default:T((()=>[E(l,{class:"uv-input__content"},{default:T((()=>[E(l,{class:"uv-input__content__prefix-icon"},{default:T((()=>[U(e.$slots,"prefix",{},(()=>[e.prefixIcon?(P(),I(s,{key:0,name:e.prefixIcon,size:"18",customStyle:e.prefixIconStyle},null,8,["name","customStyle"])):O("",!0)]),!0)])),_:3}),E(l,{class:"uv-input__content__field-wrapper",onClick:a.clickHandler},{default:T((()=>[E(r,{class:"uv-input__content__field-wrapper__field",style:B([a.inputStyle]),type:e.type,focus:e.focus,cursor:e.cursor,value:n.innerValue,"auto-blur":e.autoBlur,disabled:e.disabled||e.readonly,maxlength:e.maxlength,placeholder:e.placeholder,"placeholder-style":e.placeholderStyle,"placeholder-class":e.placeholderClass,"confirm-type":e.confirmType,"confirm-hold":e.confirmHold,"hold-keyboard":e.holdKeyboard,"cursor-spacing":e.cursorSpacing,"adjust-position":e.adjustPosition,"selection-end":e.selectionEnd,"selection-start":e.selectionStart,password:e.password||"password"===e.type||void 0,ignoreCompositionEvent:e.ignoreCompositionEvent,onInput:a.onInput,onBlur:a.onBlur,onFocus:a.onFocus,onConfirm:a.onConfirm,onKeyboardheightchange:a.onkeyboardheightchange},null,8,["style","type","focus","cursor","value","auto-blur","disabled","maxlength","placeholder","placeholder-style","placeholder-class","confirm-type","confirm-hold","hold-keyboard","cursor-spacing","adjust-position","selection-end","selection-start","password","ignoreCompositionEvent","onInput","onBlur","onFocus","onConfirm","onKeyboardheightchange"])])),_:1},8,["onClick"]),a.isShowClear?(P(),I(l,{key:0,class:"uv-input__content__clear",onClick:a.onClear},{default:T((()=>[E(s,{name:"close",size:"11",color:"#ffffff",customStyle:"line-height: 12px"})])),_:1},8,["onClick"])):O("",!0),E(l,{class:"uv-input__content__subfix-icon"},{default:T((()=>[U(e.$slots,"suffix",{},(()=>[e.suffixIcon?(P(),I(s,{key:0,name:e.suffixIcon,size:"18",customStyle:e.suffixIconStyle},null,8,["name","customStyle"])):O("",!0)]),!0)])),_:3})])),_:3})])),_:3},8,["class","style"])}],["__scopeId","data-v-6f82681c"]]);class ve{constructor(e,t){this.options=e,this.animation=L({...e}),this.currentStepAnimates={},this.next=0,this.$=t}_nvuePushAnimates(e,t){let i=this.currentStepAnimates[this.next],o={};if(o=i||{styles:{},config:{}},be.includes(e)){o.styles.transform||(o.styles.transform="");let i="";"rotate"===e&&(i="deg"),o.styles.transform+=`${e}(${t+i}) `}else o.styles[e]=`${t}`;this.currentStepAnimates[this.next]=o}_animateRun(e={},t={}){let i=this.$.$refs.ani.ref;if(i)return new Promise(((o,n)=>{nvueAnimation.transition(i,{styles:e,...t},(e=>{o()}))}))}_nvueNextAnimate(e,t=0,i){let o=e[t];if(o){let{styles:n,config:a}=o;this._animateRun(n,a).then((()=>{t+=1,this._nvueNextAnimate(e,t,i)}))}else this.currentStepAnimates={},"function"==typeof i&&i(),this.isEnd=!0}step(e={}){return this.animation.step(e),this}run(e){this.$.animationData=this.animation.export(),this.$.timer=setTimeout((()=>{"function"==typeof e&&e()}),this.$.durationTime)}}const be=["matrix","matrix3d","rotate","rotate3d","rotateX","rotateY","rotateZ","scale","scale3d","scaleX","scaleY","scaleZ","skew","skewX","skewY","translate","translate3d","translateX","translateY","translateZ"];function _e(e,t){if(t)return clearTimeout(t.timer),new ve(e,t)}be.concat(["opacity","backgroundColor"],["width","height","left","right","top","bottom"]).forEach((e=>{ve.prototype[e]=function(...t){return this.animation[e](...t),this}}));const Se=he({name:"uv-transition",mixins:[$,z],emits:["click","change"],props:{show:{type:Boolean,default:!1},mode:{type:[Array,String,null],default:()=>"fade"},duration:{type:[String,Number],default:300},timingFunction:{type:String,default:"ease-out"},customClass:{type:String,default:""},cellChild:{type:Boolean,default:!1}},data:()=>({isShow:!1,transform:"",opacity:1,animationData:{},durationTime:300,config:{}}),watch:{show:{handler(e){e?this.open():this.isShow&&this.close()},immediate:!0}},computed:{transformStyles(){const e={transform:this.transform,opacity:this.opacity,...this.$uv.addStyle(this.customStyle),"transition-duration":this.duration/1e3+"s"};return this.$uv.addStyle(e,"string")}},created(){this.config={duration:this.duration,timingFunction:this.timingFunction,transformOrigin:"50% 50%",delay:0},this.durationTime=this.duration},methods:{init(e={}){e.duration&&(this.durationTime=e.duration),this.animation=_e(Object.assign(this.config,e),this)},onClick(){this.$emit("click",{detail:this.isShow})},step(e,t={}){if(this.animation){for(let t in e)try{"object"==typeof e[t]?this.animation[t](...e[t]):this.animation[t](e[t])}catch(i){console.error(`方法 ${t} 不存在`)}return this.animation.step(t),this}},run(e){this.animation&&this.animation.run(e)},open(){clearTimeout(this.timer),this.transform="",this.isShow=!0;let{opacity:e,transform:t}=this.styleInit(!1);void 0!==e&&(this.opacity=e),this.transform=t,this.$nextTick((()=>{this.timer=setTimeout((()=>{this.animation=_e(this.config,this),this.tranfromInit(!1).step(),this.animation.run(),this.opacity=1,this.$emit("change",{detail:this.isShow}),this.transform=""}),20)}))},close(e){this.animation&&this.tranfromInit(!0).step().run((()=>{this.isShow=!1,this.animationData=null,this.animation=null;let{opacity:e,transform:t}=this.styleInit(!1);this.opacity=e||1,this.transform=t,this.$emit("change",{detail:this.isShow})}))},styleInit(e){let t={transform:""},i=(e,i)=>{"fade"===i?t.opacity=this.animationType(e)[i]:t.transform+=this.animationType(e)[i]+" "};return"string"==typeof this.mode?i(e,this.mode):this.mode.forEach((t=>{i(e,t)})),t},tranfromInit(e){let t=(e,t)=>{let i=null;"fade"===t?i=e?0:1:(i=e?"-100%":"0","zoom-in"===t&&(i=e?.8:1),"zoom-out"===t&&(i=e?1.2:1),"slide-right"===t&&(i=e?"100%":"0"),"slide-bottom"===t&&(i=e?"100%":"0")),this.animation[this.animationMode()[t]](i)};return"string"==typeof this.mode?t(e,this.mode):this.mode.forEach((i=>{t(e,i)})),this.animation},animationType:e=>({fade:e?1:0,"slide-top":`translateY(${e?"0":"-100%"})`,"slide-right":`translateX(${e?"0":"100%"})`,"slide-bottom":`translateY(${e?"0":"100%"})`,"slide-left":`translateX(${e?"0":"-100%"})`,"zoom-in":`scaleX(${e?1:.8}) scaleY(${e?1:.8})`,"zoom-out":`scaleX(${e?1:1.2}) scaleY(${e?1:1.2})`}),animationMode:()=>({fade:"opacity","slide-top":"translateY","slide-right":"translateX","slide-bottom":"translateY","slide-left":"translateX","zoom-in":"scale","zoom-out":"scale"}),toLine:e=>e.replace(/([A-Z])/g,"-$1").toLowerCase()}},[["render",function(e,t,i,o,n,a){const s=j;return n.isShow?(P(),I(s,{key:0,ref:"ani",animation:n.animationData,class:D(i.customClass),style:B(a.transformStyles),onClick:a.onClick},{default:T((()=>[U(e.$slots,"default")])),_:3},8,["animation","class","style","onClick"])):O("",!0)}]]);const we=he({name:"uv-line",mixins:[$,z,{props:{color:{type:String,default:"#d6d7d9"},length:{type:[String,Number],default:"100%"},direction:{type:String,default:"row"},hairline:{type:Boolean,default:!0},margin:{type:[String,Number],default:0},dashed:{type:Boolean,default:!1},...uni.$uvCommonProps,...null==(a=null==(n=uni.$uv)?void 0:n.props)?void 0:a.line}}],computed:{lineStyle(){const e={};return e.margin=this.margin,"row"===this.direction?(e.borderBottomWidth="1px",e.borderBottomStyle=this.dashed?"dashed":"solid",e.width=this.$uv.addUnit(this.length),this.hairline&&(e.transform="scaleY(0.5)")):(e.borderLeftWidth="1px",e.borderLeftStyle=this.dashed?"dashed":"solid",e.height=this.$uv.addUnit(this.length),this.hairline&&(e.transform="scaleX(0.5)")),e.borderColor=this.color,this.$uv.deepMerge(e,this.$uv.addStyle(this.customStyle))}}},[["render",function(e,t,i,o,n,a){const s=j;return P(),I(s,{class:"uv-line",style:B([a.lineStyle])},null,8,["style"])}],["__scopeId","data-v-c38927ba"]]);const xe=he({name:"uv-form-item",emits:["click"],mixins:[$,z,{props:{label:{type:String,default:""},prop:{type:String,default:""},borderBottom:{type:[Boolean],default:!1},labelPosition:{type:String,default:""},labelWidth:{type:[String,Number],default:""},rightIcon:{type:String,default:""},leftIcon:{type:String,default:""},required:{type:Boolean,default:!1},leftIconStyle:{type:[String,Object],default:""},...uni.$uvCommonProps,...null==(l=null==(s=uni.$uv)?void 0:s.props)?void 0:l.formItem}}],data:()=>({message:"",parentData:{labelPosition:"left",labelAlign:"left",labelStyle:{},labelWidth:45,errorType:"message"}}),created(){this.init()},methods:{init(){this.updateParentData(),this.parent||this.$uv.error("uv-form-item需要结合uv-form组件使用")},updateParentData(){this.getParentData("uv-form")},clearValidate(){this.message=null},resetField(){const e=this.$uv.getProperty(this.parent.originalModel,this.prop);this.$uv.setProperty(this.parent.model,this.prop,e),this.message=null},clickHandler(){this.$emit("click")}}},[["render",function(e,t,i,o,n,a){const s=F,l=R(V("uv-icon"),ye),r=j,u=R(V("uv-transition"),Se),c=R(V("uv-line"),we);return P(),I(r,{class:"uv-form-item"},{default:T((()=>[E(r,{class:"uv-form-item__body",onClick:a.clickHandler,style:B([e.$uv.addStyle(e.customStyle),{flexDirection:"left"===(e.labelPosition||n.parentData.labelPosition)?"row":"column"}])},{default:T((()=>[U(e.$slots,"label",{},(()=>[e.required||e.leftIcon||e.label?(P(),I(r,{key:0,class:"uv-form-item__body__left",style:B({width:e.$uv.addUnit(e.labelWidth||n.parentData.labelWidth),marginBottom:"left"===n.parentData.labelPosition?0:"5px"})},{default:T((()=>[E(r,{class:"uv-form-item__body__left__content"},{default:T((()=>[e.required?(P(),I(s,{key:0,class:"uv-form-item__body__left__content__required"},{default:T((()=>[q("*")])),_:1})):O("",!0),e.leftIcon?(P(),I(r,{key:1,class:"uv-form-item__body__left__content__icon"},{default:T((()=>[E(l,{name:e.leftIcon,"custom-style":e.leftIconStyle},null,8,["name","custom-style"])])),_:1})):O("",!0),E(s,{class:"uv-form-item__body__left__content__label",style:B([n.parentData.labelStyle,{justifyContent:"left"===n.parentData.labelAlign?"flex-start":"center"===n.parentData.labelAlign?"center":"flex-end"}])},{default:T((()=>[q(A(e.label),1)])),_:1},8,["style"])])),_:1})])),_:1},8,["style"])):O("",!0)]),!0),E(r,{class:"uv-form-item__body__right"},{default:T((()=>[E(r,{class:"uv-form-item__body__right__content"},{default:T((()=>[E(r,{class:"uv-form-item__body__right__content__slot"},{default:T((()=>[U(e.$slots,"default",{},void 0,!0)])),_:3}),E(r,{class:"item__body__right__content__icon"},{default:T((()=>[U(e.$slots,"right",{},void 0,!0)])),_:3})])),_:3})])),_:3})])),_:3},8,["onClick","style"]),U(e.$slots,"error",{},(()=>[n.message&&"message"===n.parentData.errorType?(P(),I(u,{key:0,show:!0,duration:100,mode:"fade"},{default:T((()=>[E(s,{class:"uv-form-item__body__right__message",style:B({marginLeft:e.$uv.addUnit("top"===n.parentData.labelPosition?0:e.labelWidth||n.parentData.labelWidth)})},{default:T((()=>[q(A(n.message),1)])),_:1},8,["style"])])),_:1})):O("",!0)]),!0),e.borderBottom?(P(),I(c,{key:0,color:n.message&&"border-bottom"===n.parentData.errorType?"#f56c6c":"#d6d7d9"},null,8,["color"])):O("",!0)])),_:3})}],["__scopeId","data-v-e51377d4"]]);const Ce=he({name:"uv-radio",mixins:[$,z,{props:{name:{type:[String,Number,Boolean],default:""},shape:{type:String,default:""},disabled:{type:[String,Boolean],default:""},labelDisabled:{type:[String,Boolean],default:""},activeColor:{type:String,default:""},inactiveColor:{type:String,default:""},iconSize:{type:[String,Number],default:""},labelSize:{type:[String,Number],default:""},label:{type:[String,Number,Boolean],default:""},size:{type:[String,Number],default:""},iconColor:{type:String,default:""},labelColor:{type:String,default:""},...uni.$uvCommonProps,...null==(u=null==(r=uni.$uv)?void 0:r.props)?void 0:u.radio}}],data:()=>({checked:!1,parentData:{iconSize:12,labelSize:14,labelColor:"#303133",labelDisabled:null,disabled:null,shape:null,activeColor:null,inactiveColor:null,size:18,value:null,modelValue:null,iconColor:null,placement:"row",borderBottom:!1,iconPlacement:"left"}}),computed:{elDisabled(){return""!==this.disabled?this.disabled:null!==this.parentData.disabled&&this.parentData.disabled},elLabelDisabled(){return""!==this.labelDisabled?this.labelDisabled:null!==this.parentData.labelDisabled&&this.parentData.labelDisabled},elSize(){return this.size?this.size:this.parentData.size?this.parentData.size:21},elIconSize(){return this.iconSize?this.iconSize:this.parentData.iconSize?this.parentData.iconSize:12},elActiveColor(){return this.activeColor?this.activeColor:this.parentData.activeColor?this.parentData.activeColor:"#2979ff"},elInactiveColor(){return this.inactiveColor?this.inactiveColor:this.parentData.inactiveColor?this.parentData.inactiveColor:"#c8c9cc"},elLabelColor(){return this.labelColor?this.labelColor:this.parentData.labelColor?this.parentData.labelColor:"#606266"},elShape(){return this.shape?this.shape:this.parentData.shape?this.parentData.shape:"circle"},elLabelSize(){return this.$uv.addUnit(this.labelSize?this.labelSize:this.parentData.labelSize?this.parentData.labelSize:"15")},elIconColor(){const e=this.iconColor?this.iconColor:this.parentData.iconColor?this.parentData.iconColor:"#ffffff";return this.elDisabled?this.checked?this.elInactiveColor:"transparent":this.checked?e:"transparent"},iconClasses(){let e=[];return e.push("uv-radio__icon-wrap--"+this.elShape),this.elDisabled&&e.push("uv-radio__icon-wrap--disabled"),this.checked&&this.elDisabled&&e.push("uv-radio__icon-wrap--disabled--checked"),e},iconWrapStyle(){const e={};return e.backgroundColor=this.checked&&!this.elDisabled?this.elActiveColor:"#ffffff",e.borderColor=this.checked&&!this.elDisabled?this.elActiveColor:this.elInactiveColor,e.width=this.$uv.addUnit(this.elSize),e.height=this.$uv.addUnit(this.elSize),"right"===this.parentData.iconPlacement&&(e.marginRight=0),e},radioStyle(){const e={};return this.parentData.borderBottom&&"row"===this.parentData.placement&&this.$uv.error("检测到您将borderBottom设置为true，需要同时将uv-radio-group的placement设置为column才有效"),this.parentData.borderBottom&&"column"===this.parentData.placement&&(e.paddingBottom="ios"===this.$uv.os()?"12px":"8px"),this.$uv.deepMerge(e,this.$uv.addStyle(this.customStyle))}},created(){this.init()},methods:{init(){this.updateParentData(),this.parent||this.$uv.error("uv-radio必须搭配uv-radio-group组件使用"),this.$nextTick((()=>{let e=null;e=this.parentData.modelValue,this.checked=this.name===e}))},updateParentData(){this.getParentData("uv-radio-group")},iconClickHandler(e){this.preventEvent(e),this.elDisabled||this.setRadioCheckedStatus()},wrapperClickHandler(e){"right"===this.parentData.iconPlacement&&this.iconClickHandler(e)},labelClickHandler(e){this.preventEvent(e),this.elLabelDisabled||this.elDisabled||this.setRadioCheckedStatus()},emitEvent(){this.checked||(this.$emit("change",this.name),this.$nextTick((()=>{this.$uv.formValidate(this,"change")})))},setRadioCheckedStatus(){this.emitEvent(),this.checked=!0,"function"==typeof this.parent.unCheckedOther&&this.parent.unCheckedOther(this)}}},[["render",function(e,t,i,o,n,a){const s=R(V("uv-icon"),ye),l=j,r=F;return P(),I(l,{class:D(["uv-radio",[`uv-radio-label--${n.parentData.iconPlacement}`,n.parentData.borderBottom&&"column"===n.parentData.placement&&"uv-border-bottom"]]),onClick:H(a.wrapperClickHandler,["stop"]),style:B([a.radioStyle])},{default:T((()=>[E(l,{class:D(["uv-radio__icon-wrap",a.iconClasses]),onClick:H(a.iconClickHandler,["stop"]),style:B([a.iconWrapStyle])},{default:T((()=>[U(e.$slots,"icon",{},(()=>[E(s,{class:"uv-radio__icon-wrap__icon",name:"checkbox-mark",size:a.elIconSize,color:a.elIconColor},null,8,["size","color"])]),!0)])),_:3},8,["onClick","class","style"]),E(l,{class:"uv-radio__label-wrap",onClick:H(a.labelClickHandler,["stop"])},{default:T((()=>[U(e.$slots,"default",{},(()=>[E(r,{style:B({color:a.elDisabled?a.elInactiveColor:a.elLabelColor,fontSize:a.elLabelSize,lineHeight:a.elLabelSize})},{default:T((()=>[q(A(e.label),1)])),_:1},8,["style"])]),!0)])),_:3},8,["onClick"])])),_:3},8,["onClick","style","class"])}],["__scopeId","data-v-3dc28a3d"]]);const ke=he({name:"uv-loading-icon",mixins:[$,z,{props:{show:{type:Boolean,default:!0},color:{type:String,default:"#909193"},textColor:{type:String,default:"#909193"},vertical:{type:Boolean,default:!1},mode:{type:String,default:"spinner"},size:{type:[String,Number],default:24},textSize:{type:[String,Number],default:15},textStyle:{type:Object,default:()=>({})},text:{type:[String,Number],default:""},timingFunction:{type:String,default:"linear"},duration:{type:[String,Number],default:1200},inactiveColor:{type:String,default:""},...uni.$uvCommonProps,...null==(d=null==(c=uni.$uv)?void 0:c.props)?void 0:d.loadingIcon}}],data:()=>({array12:Array.from({length:12}),aniAngel:360,webviewHide:!1,loading:!1}),computed:{otherBorderColor(){const e=W(this.color,"#ffffff",100)[80];return"circle"===this.mode?this.inactiveColor?this.inactiveColor:e:"transparent"}},watch:{show(e){}},mounted(){this.init()},methods:{init(){setTimeout((()=>{}),20)},addEventListenerToWebview(){const e=Y(),t=e[e.length-1].$getAppWebview();t.addEventListener("hide",(()=>{this.webviewHide=!0})),t.addEventListener("show",(()=>{this.webviewHide=!1}))}}},[["render",function(e,t,i,o,n,a){const s=j,l=F;return e.show?(P(),I(s,{key:0,class:D(["uv-loading-icon",[e.vertical&&"uv-loading-icon--vertical"]]),style:B([e.$uv.addStyle(e.customStyle)])},{default:T((()=>[n.webviewHide?O("",!0):(P(),I(s,{key:0,class:D(["uv-loading-icon__spinner",[`uv-loading-icon__spinner--${e.mode}`]]),ref:"ani",style:B({color:e.color,width:e.$uv.addUnit(e.size),height:e.$uv.addUnit(e.size),borderTopColor:e.color,borderBottomColor:a.otherBorderColor,borderLeftColor:a.otherBorderColor,borderRightColor:a.otherBorderColor,"animation-duration":`${e.duration}ms`,"animation-timing-function":"semicircle"===e.mode||"circle"===e.mode?e.timingFunction:""})},{default:T((()=>["spinner"===e.mode?(P(!0),X(K,{key:0},Z(n.array12,((e,t)=>(P(),I(s,{key:t,class:"uv-loading-icon__dot"})))),128)):O("",!0)])),_:1},8,["class","style"])),e.text?(P(),I(l,{key:1,class:"uv-loading-icon__text",style:B([{fontSize:e.$uv.addUnit(e.textSize),color:e.textColor},e.$uv.addStyle(e.textStyle)])},{default:T((()=>[q(A(e.text),1)])),_:1},8,["style"])):O("",!0)])),_:1},8,["style","class"])):O("",!0)}],["__scopeId","data-v-0b726165"]]);const $e=he({name:"uv-overlay",emits:["click"],mixins:[$,z,{props:{show:{type:Boolean,default:!1},zIndex:{type:[String,Number],default:10070},duration:{type:[String,Number],default:300},opacity:{type:[String,Number],default:.5},...uni.$uvCommonProps,...null==(h=null==(p=uni.$uv)?void 0:p.props)?void 0:h.overlay}}],watch:{show(e){document.querySelector("body").style.overflow=e?"hidden":""}},computed:{overlayStyle(){const e={position:"fixed",top:0,left:0,right:0,zIndex:this.zIndex,bottom:0,"background-color":`rgba(0, 0, 0, ${this.opacity})`};return this.$uv.deepMerge(e,this.$uv.addStyle(this.customStyle))}},methods:{clickHandler(){this.$emit("click")},clear(){}}},[["render",function(e,t,i,o,n,a){const s=R(V("uv-transition"),Se);return P(),I(s,{show:e.show,mode:"fade","custom-class":"uv-overlay",duration:e.duration,"custom-style":a.overlayStyle,onClick:a.clickHandler,onTouchmove:H(a.clear,["stop","prevent"])},{default:T((()=>[U(e.$slots,"default",{},void 0,!0)])),_:3},8,["show","duration","custom-style","onClick","onTouchmove"])}],["__scopeId","data-v-60064041"]]);const ze=he({name:"uv-status-bar",mixins:[$,z,{props:{bgColor:{type:String,default:"transparent"}}}],data:()=>({}),computed:{style(){const e={};return e.height=this.$uv.addUnit(this.$uv.sys().statusBarHeight,"px"),this.bgColor&&(this.bgColor.indexOf("gradient")>-1?e.backgroundImage=this.bgColor:e.background=this.bgColor),this.$uv.deepMerge(e,this.$uv.addStyle(this.customStyle))}}},[["render",function(e,t,i,o,n,a){const s=j;return P(),I(s,{style:B([a.style]),class:"uv-status-bar"},{default:T((()=>[U(e.$slots,"default",{},void 0,!0)])),_:3},8,["style"])}],["__scopeId","data-v-9583337f"]]);const Pe=he({name:"uv-safe-bottom",mixins:[$,z],data:()=>({safeAreaBottomHeight:0,isNvue:!1}),computed:{style(){return this.$uv.deepMerge({},this.$uv.addStyle(this.customStyle))}},mounted(){}},[["render",function(e,t,i,o,n,a){const s=j;return P(),I(s,{class:D(["uv-safe-bottom",[!n.isNvue&&"uv-safe-area-inset-bottom"]]),style:B([a.style])},null,8,["style","class"])}],["__scopeId","data-v-80d87af6"]]);const Ie=he({name:"uv-popup",components:{keypress:{name:"Keypress",props:{disable:{type:Boolean,default:!1}},mounted(){const e={esc:["Esc","Escape"],tab:"Tab",enter:"Enter",space:[" ","Spacebar"],up:["Up","ArrowUp"],left:["Left","ArrowLeft"],right:["Right","ArrowRight"],down:["Down","ArrowDown"],delete:["Backspace","Delete","Del"]};document.addEventListener("keyup",(t=>{if(this.disable)return;const i=Object.keys(e).find((i=>{const o=t.key,n=e[i];return n===o||Array.isArray(n)&&n.includes(o)}));i&&setTimeout((()=>{this.$emit(i,{})}),0)}))},render:()=>{}}},mixins:[$,z],emits:["change","maskClick"],props:{mode:{type:String,default:"center"},duration:{type:[String,Number],default:300},zIndex:{type:[String,Number],default:997},bgColor:{type:String,default:"#ffffff"},safeArea:{type:Boolean,default:!0},overlay:{type:Boolean,default:!0},closeOnClickOverlay:{type:Boolean,default:!0},overlayOpacity:{type:[Number,String],default:.4},overlayStyle:{type:[Object,String],default:""},safeAreaInsetBottom:{type:Boolean,default:!0},safeAreaInsetTop:{type:Boolean,default:!1},closeable:{type:Boolean,default:!1},closeIconPos:{type:String,default:"top-right"},zoom:{type:Boolean,default:!0},round:{type:[Number,String],default:0},...uni.$uvCommonProps,...null==(m=null==(f=uni.$uv)?void 0:f.props)?void 0:m.popup},watch:{type:{handler:function(e){this.config[e]&&this[this.config[e]](!0)},immediate:!0},isDesktop:{handler:function(e){this.config[e]&&this[this.config[this.mode]](!0)},immediate:!0},showPopup(e){document.getElementsByTagName("body")[0].style.overflow=e?"hidden":"visible"}},data(){return{ani:[],showPopup:!1,showTrans:!1,popupWidth:0,popupHeight:0,config:{top:"top",bottom:"bottom",center:"center",left:"left",right:"right",message:"top",dialog:"center",share:"bottom"},transitionStyle:{position:"fixed",left:0,right:0},maskShow:!0,mkclick:!0,popupClass:this.isDesktop?"fixforpc-top":"top",direction:""}},computed:{isDesktop(){return this.popupWidth>=500&&this.popupHeight>=500},bg(){return""===this.bgColor||"none"===this.bgColor||this.$uv.getPx(this.round)>0?"transparent":this.bgColor},contentStyle(){const e={};if(this.bgColor&&(e.backgroundColor=this.bg),this.round){const t=this.$uv.addUnit(this.round),i=this.direction?this.direction:this.mode;e.backgroundColor=this.bgColor,"top"===i?(e.borderBottomLeftRadius=t,e.borderBottomRightRadius=t):"bottom"===i?(e.borderTopLeftRadius=t,e.borderTopRightRadius=t):"center"===i&&(e.borderRadius=t)}return this.$uv.deepMerge(e,this.$uv.addStyle(this.customStyle))}},unmounted(){this.setH5Visible()},created(){this.messageChild=null,this.clearPropagation=!1},methods:{setH5Visible(){document.getElementsByTagName("body")[0].style.overflow="visible"},closeMask(){this.maskShow=!1},clear(e){e.stopPropagation(),this.clearPropagation=!0},open(e){if(this.showPopup)return;if(e&&-1!==["top","center","bottom","left","right","message","dialog","share"].indexOf(e)?this.direction=e:e=this.mode,!this.config[e])return this.$uv.error(`缺少类型：${e}`);this[this.config[e]](),this.$emit("change",{show:!0,type:e})},close(e){this.showTrans=!1,this.$emit("change",{show:!1,type:this.mode}),clearTimeout(this.timer),this.timer=setTimeout((()=>{this.showPopup=!1}),300)},touchstart(){this.clearPropagation=!1},onTap(){this.clearPropagation?this.clearPropagation=!1:(this.$emit("maskClick"),this.closeOnClickOverlay&&this.close())},top(e){this.popupClass=this.isDesktop?"fixforpc-top":"top",this.ani=["slide-top"],this.transitionStyle={position:"fixed",zIndex:this.zIndex,left:0,right:0,backgroundColor:this.bg},e||(this.showPopup=!0,this.showTrans=!0,this.$nextTick((()=>{this.messageChild&&"message"===this.mode&&this.messageChild.timerClose()})))},bottom(e){this.popupClass="bottom",this.ani=["slide-bottom"],this.transitionStyle={position:"fixed",zIndex:this.zIndex,left:0,right:0,bottom:0,backgroundColor:this.bg},e||(this.showPopup=!0,this.showTrans=!0)},center(e){this.popupClass="center",this.ani=this.zoom?["zoom-in","fade"]:["fade"],this.transitionStyle={position:"fixed",zIndex:this.zIndex,display:"flex",flexDirection:"column",bottom:0,left:0,right:0,top:0,justifyContent:"center",alignItems:"center"},e||(this.showPopup=!0,this.showTrans=!0)},left(e){this.popupClass="left",this.ani=["slide-left"],this.transitionStyle={position:"fixed",zIndex:this.zIndex,left:0,bottom:0,top:0,backgroundColor:this.bg,display:"flex",flexDirection:"column"},e||(this.showPopup=!0,this.showTrans=!0)},right(e){this.popupClass="right",this.ani=["slide-right"],this.transitionStyle={position:"fixed",zIndex:this.zIndex,bottom:0,right:0,top:0,backgroundColor:this.bg,display:"flex",flexDirection:"column"},e||(this.showPopup=!0,this.showTrans=!0)}}},[["render",function(e,t,i,o,n,a){const s=R(V("uv-overlay"),$e),l=R(V("uv-status-bar"),ze),r=R(V("uv-safe-bottom"),Pe),u=R(V("uv-icon"),ye),c=j,d=R(V("uv-transition"),Se),p=J("keypress");return n.showPopup?(P(),I(c,{key:0,class:D(["uv-popup",[n.popupClass,a.isDesktop?"fixforpc-z-index":""]]),style:B([{zIndex:i.zIndex}])},{default:T((()=>[E(c,{onTouchstart:a.touchstart},{default:T((()=>[n.maskShow&&i.overlay?(P(),I(s,{key:"1",show:n.showTrans,duration:i.duration,"custom-style":i.overlayStyle,opacity:i.overlayOpacity,zIndex:i.zIndex,onClick:a.onTap},null,8,["show","duration","custom-style","opacity","zIndex","onClick"])):O("",!0),E(d,{key:"2",mode:n.ani,name:"content","custom-style":n.transitionStyle,duration:i.duration,show:n.showTrans,onClick:a.onTap},{default:T((()=>[E(c,{class:D(["uv-popup__content",[n.popupClass]]),style:B([a.contentStyle]),onClick:a.clear},{default:T((()=>[i.safeAreaInsetTop?(P(),I(l,{key:0})):O("",!0),U(e.$slots,"default",{},void 0,!0),i.safeAreaInsetBottom?(P(),I(r,{key:1})):O("",!0),i.closeable?(P(),I(c,{key:2,onClick:H(a.close,["stop"]),class:D(["uv-popup__content__close",["uv-popup__content__close--"+i.closeIconPos]]),"hover-class":"uv-popup__content__close--hover","hover-stay-time":"150"},{default:T((()=>[E(u,{name:"close",color:"#909399",size:"18",bold:""})])),_:1},8,["onClick","class"])):O("",!0)])),_:3},8,["style","class","onClick"])])),_:3},8,["mode","custom-style","duration","show","onClick"])])),_:3},8,["onTouchstart"]),n.maskShow?(P(),I(p,{key:0,onEsc:a.onTap},null,8,["onEsc"])):O("",!0)])),_:3},8,["class","style"])):O("",!0)}],["__scopeId","data-v-c4060c06"]]);const Te=he({props:{src:{type:String,default:""},autoplay:{type:Boolean,default:!0}},data:()=>({videoSrc:"",show:!1}),computed:{getSec(){return this.src||this.videoSrc}},methods:{open(e){this.videoSrc=e,this.$refs.popup.open()},close(){this.$refs.popup.close()},change(e){this.show=e.show}}},[["render",function(e,t,i,o,n,a){const s=G,l=j,r=R(V("uv-popup"),Ie);return P(),I(r,{ref:"popup",onChange:a.change},{default:T((()=>[n.show?(P(),I(l,{key:0,class:"video-view"},{default:T((()=>[E(s,{class:"video",src:a.getSec,autoplay:i.autoplay},null,8,["src","autoplay"])])),_:1})):O("",!0)])),_:1},8,["onChange"])}],["__scopeId","data-v-5f5c21a6"]]);function Be(e,t){return["[object Object]","[object File]"].includes(Object.prototype.toString.call(e))?Object.keys(e).reduce(((i,o)=>(t.includes(o)||(i[o]=e[o]),i)),{}):{}}function De(e){return e.tempFiles.map((e=>({...Be(e,["path"]),url:e.path,size:e.size,name:e.name,type:e.type})))}function qe({accept:e,multiple:t,capture:i,compressed:o,maxDuration:n,sizeType:a,camera:s,maxCount:l}){return new Promise(((r,u)=>{switch(e){case"image":te({count:t?Math.min(l,9):1,sourceType:i,sizeType:a,success:e=>r(function(e){return e.tempFiles.map((e=>({...Be(e,["path"]),type:"image",url:e.path,thumb:e.path,size:e.size,name:e.name})))}(e)),fail:u});break;case"video":ee({sourceType:i,compressed:o,maxDuration:n,camera:s,success:e=>r(function(e){return[{...Be(e,["tempFilePath","thumbTempFilePath","errMsg"]),type:"video",url:e.tempFilePath,thumb:e.thumbTempFilePath,size:e.size,name:e.name}]}(e)),fail:u});break;case"file":Q({count:t?l:1,type:e,success:e=>r(De(e)),fail:u});break;default:Q({count:t?l:1,type:"all",success:e=>r(De(e)),fail:u})}}))}const Ae=he({name:"uv-upload",emits:["error","beforeRead","oversize","afterRead","delete","clickPreview"],mixins:[$,z,{watch:{accept:{immediate:!0,handler(e){}}}},{props:{accept:{type:String,default:"image"},capture:{type:[String,Array],default:()=>["album","camera"]},compressed:{type:Boolean,default:!0},camera:{type:String,default:"back"},maxDuration:{type:Number,default:60},uploadIcon:{type:String,default:"camera-fill"},uploadIconColor:{type:String,default:"#D3D4D6"},useBeforeRead:{type:Boolean,default:!1},afterRead:{type:Function,default:null},beforeRead:{type:Function,default:null},previewFullImage:{type:Boolean,default:!0},previewFullVideo:{type:Boolean,default:!0},maxCount:{type:[String,Number],default:52},disabled:{type:Boolean,default:!1},imageMode:{type:String,default:"aspectFill"},name:{type:String,default:""},sizeType:{type:Array,default:()=>["original","compressed"]},multiple:{type:Boolean,default:!1},deletable:{type:Boolean,default:!0},maxSize:{type:[String,Number],default:Number.MAX_VALUE},fileList:{type:Array,default:()=>[]},uploadText:{type:String,default:""},width:{type:[String,Number],default:80},height:{type:[String,Number],default:80},previewImage:{type:Boolean,default:!0},...uni.$uvCommonProps,...null==(g=null==(y=uni.$uv)?void 0:y.props)?void 0:g.upload}}],data:()=>({lists:[],isInCount:!0}),watch:{fileList:{deep:!0,immediate:!0,handler(){this.formatFileList()}},deletable(e){e||this.lists.map((e=>{e.deletable=this.deletable}))}},methods:{formatFileList(){const{fileList:e=[],maxCount:t}=this,i=e.map((e=>Object.assign(Object.assign({},e),{isImage:"image"===this.accept||ie(e.url||e.thumb),isVideo:"video"===this.accept||oe(e.url||e.thumb),deletable:"boolean"==typeof e.deletable?e.deletable:this.deletable})));this.lists=i,this.isInCount=i.length<t},chooseFile(){this.timer&&clearTimeout(this.timer),this.timer=setTimeout((()=>{const{maxCount:e,multiple:t,lists:i,disabled:o}=this;if(o)return;let n;try{n=ne(this.capture)?this.capture:this.capture.split(",")}catch(a){n=[]}qe(Object.assign({accept:this.accept,multiple:this.multiple,capture:n,compressed:this.compressed,maxDuration:this.maxDuration,sizeType:this.sizeType,camera:this.camera},{maxCount:e-i.length})).then((e=>{this.onBeforeRead(t?e:e[0])})).catch((e=>{this.$emit("error",e)}))}),100)},onBeforeRead(e){const{beforeRead:t,useBeforeRead:i}=this;let o=!0;ae(t)&&(o=t(e,this.getDetail())),i&&(o=new Promise(((t,i)=>{this.$emit("beforeRead",Object.assign(Object.assign({file:e},this.getDetail()),{callback:e=>{e?t():i()}}))}))),o&&(se(o)?o.then((t=>this.onAfterRead(t||e))):this.onAfterRead(e))},getDetail(e){return{name:this.name,index:null==e?this.fileList.length:e}},onAfterRead(e){const{maxSize:t,afterRead:i}=this;(Array.isArray(e)?e.some((e=>e.size>t)):e.size>t)?this.$emit("oversize",Object.assign({file:e},this.getDetail())):("function"==typeof i&&i(e,this.getDetail()),this.$emit("afterRead",Object.assign({file:e},this.getDetail())))},deleteItem(e){this.$emit("delete",Object.assign(Object.assign({},this.getDetail(e)),{file:this.fileList[e]}))},onPreviewImage(e,t){const i=this.$uv.deepClone(this.lists);i.map(((e,i)=>{i==t&&(e.current=!0)}));const o=i.filter((e=>e.isImage)).findIndex((e=>e.current));this.onClickPreview(e,t),e.isImage&&this.previewFullImage&&le({urls:this.lists.filter((e=>"image"===this.accept||ie(e.url||e.thumb))).map((e=>e.url||e.thumb)),current:o,fail(){this.$uv.toast("预览图片失败")}})},onPreviewVideo(e,t){this.onClickPreview(e,t),this.previewFullVideo&&e.isVideo&&this.$refs.previewVideo.open(e.url)},onClickPreview(e,t){this.$emit("clickPreview",Object.assign(Object.assign({},e),this.getDetail(t)))}}},[["render",function(e,t,i,o,n,a){const s=N,l=R(V("uv-icon"),ye),r=F,u=j,c=R(V("uv-loading-icon"),ke),d=R(V("uv-preview-video"),Te);return P(),I(u,{class:"uv-upload",style:B([e.$uv.addStyle(e.customStyle)])},{default:T((()=>[E(u,{class:"uv-upload__wrap"},{default:T((()=>[e.previewImage?(P(!0),X(K,{key:0},Z(n.lists,((t,i)=>(P(),I(u,{class:"uv-upload__wrap__preview",key:i},{default:T((()=>[t.isImage||t.type&&"image"===t.type?(P(),I(s,{key:0,src:t.thumb||t.url,mode:e.imageMode,class:"uv-upload__wrap__preview__image",onClick:e=>a.onPreviewImage(t,i),style:B([{width:e.$uv.addUnit(e.width),height:e.$uv.addUnit(e.height)}])},null,8,["src","mode","onClick","style"])):(P(),I(u,{key:1,class:"uv-upload__wrap__preview__other",onClick:e=>a.onPreviewVideo(t,i),style:B([{width:e.$uv.addUnit(e.width),height:e.$uv.addUnit(e.height)}])},{default:T((()=>[E(l,{color:"#80CBF9",size:"26",name:t.isVideo||t.type&&"video"===t.type?"movie":"folder"},null,8,["name"]),E(r,{class:"uv-upload__wrap__preview__other__text"},{default:T((()=>[q(A(t.isVideo||t.type&&"video"===t.type?"视频":"文件"),1)])),_:2},1024)])),_:2},1032,["onClick","style"])),"uploading"===t.status||"failed"===t.status?(P(),I(u,{key:2,class:"uv-upload__status"},{default:T((()=>[E(u,{class:"uv-upload__status__icon"},{default:T((()=>["failed"===t.status?(P(),I(l,{key:0,name:"close-circle",color:"#ffffff",size:"25"})):(P(),I(c,{key:1,size:"22",mode:"circle"}))])),_:2},1024),t.message?(P(),I(r,{key:0,class:"uv-upload__status__message"},{default:T((()=>[q(A(t.message),1)])),_:2},1024)):O("",!0)])),_:2},1024)):O("",!0),"uploading"!==t.status&&(e.deletable||t.deletable)?(P(),I(u,{key:3,class:"uv-upload__deletable",onClick:H((e=>a.deleteItem(i)),["stop"])},{default:T((()=>[E(u,{class:"uv-upload__deletable__icon"},{default:T((()=>[E(l,{name:"close",color:"#ffffff",size:"10"})])),_:1})])),_:2},1032,["onClick"])):O("",!0),"success"===t.status?(P(),I(u,{key:4,class:"uv-upload__success"},{default:T((()=>[E(u,{class:"uv-upload__success__icon"},{default:T((()=>[E(l,{name:"checkmark",color:"#ffffff",size:"12"})])),_:1})])),_:1})):O("",!0)])),_:2},1024)))),128)):O("",!0),n.isInCount?(P(),I(u,{key:1,onClick:a.chooseFile},{default:T((()=>[U(e.$slots,"default",{},(()=>[E(u,{class:D(["uv-upload__button",[e.disabled&&"uv-upload__button--disabled"]]),"hover-class":e.disabled?"":"uv-upload__button--hover","hover-stay-time":"150",onClick:H(a.chooseFile,["stop"]),style:B([{width:e.$uv.addUnit(e.width),height:e.$uv.addUnit(e.height)}])},{default:T((()=>[E(l,{name:e.uploadIcon,size:"26",color:e.uploadIconColor},null,8,["name","color"]),e.uploadText?(P(),I(r,{key:0,class:"uv-upload__button__text"},{default:T((()=>[q(A(e.uploadText),1)])),_:1})):O("",!0)])),_:1},8,["hover-class","onClick","class","style"])]),!0)])),_:3},8,["onClick"])):O("",!0)])),_:3}),E(d,{ref:"previewVideo"},null,512)])),_:3},8,["style"])}],["__scopeId","data-v-6fb5ed57"]]),Oe={props:{lang:String,sessionFrom:String,sendMessageTitle:String,sendMessagePath:String,sendMessageImg:String,showMessageCard:Boolean,appParameter:String,formType:String,openType:String}},Ne={props:{openType:String},emits:["getphonenumber","getuserinfo","error","opensetting","launchapp","contact","chooseavatar","addgroupapp","chooseaddress","subscribe","login","im"],methods:{onGetPhoneNumber(e){this.$emit("getphonenumber",e.detail)},onGetUserInfo(e){this.$emit("getuserinfo",e.detail)},onError(e){this.$emit("error",e.detail)},onOpenSetting(e){this.$emit("opensetting",e.detail)},onLaunchApp(e){this.$emit("launchapp",e.detail)},onContact(e){this.$emit("contact",e.detail)},onChooseavatar(e){this.$emit("chooseavatar",e.detail)},onAgreeprivacyauthorization(e){this.$emit("agreeprivacyauthorization",e.detail)},onAddgroupapp(e){this.$emit("addgroupapp",e.detail)},onChooseaddress(e){this.$emit("chooseaddress",e.detail)},onSubscribe(e){this.$emit("subscribe",e.detail)},onLogin(e){this.$emit("login",e.detail)},onIm(e){this.$emit("im",e.detail)}}};const Fe=he({name:"uv-button",mixins:[$,z,{props:{hairline:{type:Boolean,default:!0},type:{type:String,default:"info"},size:{type:String,default:"normal"},shape:{type:String,default:"square"},plain:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},loading:{type:Boolean,default:!1},loadingText:{type:[String,Number],default:""},loadingMode:{type:String,default:"spinner"},loadingSize:{type:[String,Number],default:14},openType:{type:String,default:""},formType:{type:String,default:""},appParameter:{type:String,default:""},hoverStopPropagation:{type:Boolean,default:!0},lang:{type:String,default:"en"},sessionFrom:{type:String,default:""},sendMessageTitle:{type:String,default:""},sendMessagePath:{type:String,default:""},sendMessageImg:{type:String,default:""},showMessageCard:{type:Boolean,default:!0},dataName:{type:String,default:""},throttleTime:{type:[String,Number],default:0},hoverStartTime:{type:[String,Number],default:0},hoverStayTime:{type:[String,Number],default:200},text:{type:[String,Number],default:""},icon:{type:String,default:""},iconSize:{type:[String,Number],default:""},iconColor:{type:String,default:"#000000"},color:{type:String,default:""},customTextStyle:{type:[Object,String],default:""},...uni.$uvCommonProps,...null==(b=null==(v=uni.$uv)?void 0:v.props)?void 0:b.button}}],emits:["click"],data:()=>({}),computed:{bemClass(){return this.color?this.bem("button",["shape","size"],["disabled","plain","hairline"]):this.bem("button",["type","shape","size"],["disabled","plain","hairline"])},loadingColor(){return this.plain?this.color?this.color:"#3c9cff":"info"===this.type?"#c9c9c9":"rgb(200, 200, 200)"},iconColorCom(){return this.iconColor?this.iconColor:this.plain?this.color?this.color:this.type:"info"===this.type?"#000000":"#ffffff"},baseColor(){let e={};return this.color&&(e.color=this.plain?this.color:"white",this.plain||(e["background-color"]=this.color),-1!==this.color.indexOf("gradient")?(e.borderTopWidth=0,e.borderRightWidth=0,e.borderBottomWidth=0,e.borderLeftWidth=0,this.plain||(e.backgroundImage=this.color)):(e.borderColor=this.color,e.borderWidth="1px",e.borderStyle="solid")),e},nvueTextStyle(){let e={};return"info"===this.type&&(e.color="#323233"),this.color&&(e.color=this.plain?this.color:"white"),e.fontSize=this.textSize+"px",e},textSize(){let e=14,{size:t}=this;return"large"===t&&(e=16),"normal"===t&&(e=14),"small"===t&&(e=12),"mini"===t&&(e=10),e},getIconSize(){const e=this.iconSize?this.iconSize:1.35*this.textSize;return this.$uv.addUnit(e)},btnWrapperStyle(){const e={},t=this.$uv.addStyle(this.customStyle);return t.width&&(e.width=t.width),e}},methods:{clickHandler(){this.disabled||this.loading||re((()=>{this.$emit("click")}),this.throttleTime)}}},[["render",function(e,t,i,o,n,a){const s=R(V("uv-loading-icon"),ke),l=F,r=R(V("uv-icon"),ye),u=ue,c=j;return P(),I(c,{class:"uv-button-wrapper",style:B([a.btnWrapperStyle])},{default:T((()=>[E(u,{"hover-start-time":Number(e.hoverStartTime),"hover-stay-time":Number(e.hoverStayTime),"form-type":e.formType,"open-type":e.openType,"app-parameter":e.appParameter,"hover-stop-propagation":e.hoverStopPropagation,"send-message-title":e.sendMessageTitle,"send-message-path":e.sendMessagePath,lang:e.lang,"data-name":e.dataName,"session-from":e.sessionFrom,"send-message-img":e.sendMessageImg,"show-message-card":e.showMessageCard,"hover-class":e.disabled||e.loading?"":"uv-button--active",class:D(["uv-button uv-reset-button",a.bemClass]),style:B([a.baseColor,e.$uv.addStyle(e.customStyle)]),onClick:a.clickHandler},{default:T((()=>[e.loading?(P(),X(K,{key:0},[E(s,{mode:e.loadingMode,size:1.15*e.loadingSize,color:a.loadingColor},null,8,["mode","size","color"]),E(l,{class:"uv-button__loading-text",style:B([{fontSize:a.textSize+"px"},e.$uv.addStyle(e.customTextStyle)])},{default:T((()=>[q(A(e.loadingText||e.text),1)])),_:1},8,["style"])],64)):(P(),X(K,{key:1},[e.icon?(P(),I(r,{key:0,name:e.icon,color:a.iconColorCom,size:a.getIconSize,customStyle:{marginRight:"2px"}},null,8,["name","color","size"])):O("",!0),U(e.$slots,"default",{},(()=>[E(l,{class:"uv-button__text",style:B([{fontSize:a.textSize+"px"},e.$uv.addStyle(e.customTextStyle)])},{default:T((()=>[q(A(e.text),1)])),_:1},8,["style"])]),!0),U(e.$slots,"suffix",{},void 0,!0)],64))])),_:3},8,["hover-start-time","hover-stay-time","form-type","open-type","app-parameter","hover-stop-propagation","send-message-title","send-message-path","lang","data-name","session-from","send-message-img","show-message-card","hover-class","style","onClick","class"])])),_:3},8,["style"])}],["__scopeId","data-v-6b462a83"]]),je={props:{model:{type:Object,default:()=>({})},rules:{type:[Object,Function,Array],default:()=>({})},errorType:{type:String,default:"message"},borderBottom:{type:Boolean,default:!0},labelPosition:{type:String,default:"left"},labelWidth:{type:[String,Number],default:45},labelAlign:{type:String,default:"left"},labelStyle:{type:Object,default:()=>({})},...uni.$uvCommonProps,...null==(S=null==(_=uni.$uv)?void 0:_.props)?void 0:S.form}};const Re=/%[sdj%]/g;let Ve=function(){};function Ee(e){if(!e||!e.length)return null;const t={};return e.forEach((e=>{const{field:i}=e;t[i]=t[i]||[],t[i].push(e)})),t}function Ue(){for(var e=arguments.length,t=new Array(e),i=0;i<e;i++)t[i]=arguments[i];let o=1;const n=t[0],a=t.length;if("function"==typeof n)return n.apply(null,t.slice(1));if("string"==typeof n){let e=String(n).replace(Re,(e=>{if("%%"===e)return"%";if(o>=a)return e;switch(e){case"%s":return String(t[o++]);case"%d":return Number(t[o++]);case"%j":try{return JSON.stringify(t[o++])}catch(i){return"[Circular]"}break;default:return e}}));for(let i=t[o];o<a;i=t[++o])e+=` ${i}`;return e}return n}function Me(e,t){return null==e||(!("array"!==t||!Array.isArray(e)||e.length)||!(!function(e){return"string"===e||"url"===e||"hex"===e||"email"===e||"pattern"===e}(t)||"string"!=typeof e||e))}function Le(e,t,i){let o=0;const n=e.length;!function a(s){if(s&&s.length)return void i(s);const l=o;o+=1,l<n?t(e[l],a):i([])}([])}function He(e,t,i,o){if(t.first){const t=new Promise(((t,n)=>{const a=function(e){const t=[];return Object.keys(e).forEach((i=>{t.push.apply(t,e[i])})),t}(e);Le(a,i,(function(e){return o(e),e.length?n({errors:e,fields:Ee(e)}):t()}))}));return t.catch((e=>e)),t}let n=t.firstFields||[];!0===n&&(n=Object.keys(e));const a=Object.keys(e),s=a.length;let l=0;const r=[],u=new Promise(((t,u)=>{const c=function(e){if(r.push.apply(r,e),l++,l===s)return o(r),r.length?u({errors:r,fields:Ee(r)}):t()};a.length||(o(r),t()),a.forEach((t=>{const o=e[t];-1!==n.indexOf(t)?Le(o,i,c):function(e,t,i){const o=[];let n=0;const a=e.length;function s(e){o.push.apply(o,e),n++,n===a&&i(o)}e.forEach((e=>{t(e,s)}))}(o,i,c)}))}));return u.catch((e=>e)),u}function We(e){return function(t){return t&&t.message?(t.field=t.field||e.fullField,t):{message:"function"==typeof t?t():t,field:t.field||e.fullField}}}function Ye(e,t){if(t)for(const i in t)if(t.hasOwnProperty(i)){const o=t[i];"object"==typeof o&&"object"==typeof e[i]?e[i]={...e[i],...o}:e[i]=o}return e}function Xe(e,t,i,o,n,a){!e.required||i.hasOwnProperty(e.field)&&!Me(t,a||e.type)||o.push(Ue(n.messages.required,e.fullField))}const Ke={email:/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,url:new RegExp("^(?!mailto:)(?:(?:http|https|ftp)://|//)(?:\\S+(?::\\S*)?@)?(?:(?:(?:[1-9]\\d?|1\\d\\d|2[01]\\d|22[0-3])(?:\\.(?:1?\\d{1,2}|2[0-4]\\d|25[0-5])){2}(?:\\.(?:[0-9]\\d?|1\\d\\d|2[0-4]\\d|25[0-4]))|(?:(?:[a-z\\u00a1-\\uffff0-9]+-*)*[a-z\\u00a1-\\uffff0-9]+)(?:\\.(?:[a-z\\u00a1-\\uffff0-9]+-*)*[a-z\\u00a1-\\uffff0-9]+)*(?:\\.(?:[a-z\\u00a1-\\uffff]{2,})))|localhost)(?::\\d{2,5})?(?:(/|\\?|#)[^\\s]*)?$","i"),hex:/^#?([a-f0-9]{6}|[a-f0-9]{3})$/i};var Ze={integer:function(e){return/^(-)?\d+$/.test(e)},float:function(e){return/^(-)?\d+(\.\d+)?$/.test(e)},array:function(e){return Array.isArray(e)},regexp:function(e){if(e instanceof RegExp)return!0;try{return!!new RegExp(e)}catch(t){return!1}},date:function(e){return"function"==typeof e.getTime&&"function"==typeof e.getMonth&&"function"==typeof e.getYear},number:function(e){return!isNaN(e)&&"number"==typeof+e},object:function(e){return"object"==typeof e&&!Ze.array(e)},method:function(e){return"function"==typeof e},email:function(e){return"string"==typeof e&&!!e.match(Ke.email)&&e.length<255},url:function(e){return"string"==typeof e&&!!e.match(Ke.url)},hex:function(e){return"string"==typeof e&&!!e.match(Ke.hex)}};const Je={required:Xe,whitespace:function(e,t,i,o,n){(/^\s+$/.test(t)||""===t)&&o.push(Ue(n.messages.whitespace,e.fullField))},type:function(e,t,i,o,n){if(e.required&&void 0===t)return void Xe(e,t,i,o,n);const a=e.type;["integer","float","array","regexp","object","method","email","number","date","url","hex"].indexOf(a)>-1?Ze[a](t)||o.push(Ue(n.messages.types[a],e.fullField,e.type)):a&&typeof t!==e.type&&o.push(Ue(n.messages.types[a],e.fullField,e.type))},range:function(e,t,i,o,n){const a="number"==typeof e.len,s="number"==typeof e.min,l="number"==typeof e.max,r=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g;let u=t,c=null;const d="number"==typeof t,p="string"==typeof t,h=Array.isArray(t);if(d?c="number":p?c="string":h&&(c="array"),!c)return!1;h&&(u=t.length),p&&(u=t.replace(r,"_").length),a?u!==e.len&&o.push(Ue(n.messages[c].len,e.fullField,e.len)):s&&!l&&u<e.min?o.push(Ue(n.messages[c].min,e.fullField,e.min)):l&&!s&&u>e.max?o.push(Ue(n.messages[c].max,e.fullField,e.max)):s&&l&&(u<e.min||u>e.max)&&o.push(Ue(n.messages[c].range,e.fullField,e.min,e.max))},enum:function(e,t,i,o,n){e.enum=Array.isArray(e.enum)?e.enum:[],-1===e.enum.indexOf(t)&&o.push(Ue(n.messages.enum,e.fullField,e.enum.join(", ")))},pattern:function(e,t,i,o,n){if(e.pattern)if(e.pattern instanceof RegExp)e.pattern.lastIndex=0,e.pattern.test(t)||o.push(Ue(n.messages.pattern.mismatch,e.fullField,t,e.pattern));else if("string"==typeof e.pattern){new RegExp(e.pattern).test(t)||o.push(Ue(n.messages.pattern.mismatch,e.fullField,t,e.pattern))}}};function Ge(e,t,i,o,n){const a=e.type,s=[];if(e.required||!e.required&&o.hasOwnProperty(e.field)){if(Me(t,a)&&!e.required)return i();Je.required(e,t,o,s,n,a),Me(t,a)||Je.type(e,t,o,s,n)}i(s)}const Qe={string:function(e,t,i,o,n){const a=[];if(e.required||!e.required&&o.hasOwnProperty(e.field)){if(Me(t,"string")&&!e.required)return i();Je.required(e,t,o,a,n,"string"),Me(t,"string")||(Je.type(e,t,o,a,n),Je.range(e,t,o,a,n),Je.pattern(e,t,o,a,n),!0===e.whitespace&&Je.whitespace(e,t,o,a,n))}i(a)},method:function(e,t,i,o,n){const a=[];if(e.required||!e.required&&o.hasOwnProperty(e.field)){if(Me(t)&&!e.required)return i();Je.required(e,t,o,a,n),void 0!==t&&Je.type(e,t,o,a,n)}i(a)},number:function(e,t,i,o,n){const a=[];if(e.required||!e.required&&o.hasOwnProperty(e.field)){if(""===t&&(t=void 0),Me(t)&&!e.required)return i();Je.required(e,t,o,a,n),void 0!==t&&(Je.type(e,t,o,a,n),Je.range(e,t,o,a,n))}i(a)},boolean:function(e,t,i,o,n){const a=[];if(e.required||!e.required&&o.hasOwnProperty(e.field)){if(Me(t)&&!e.required)return i();Je.required(e,t,o,a,n),void 0!==t&&Je.type(e,t,o,a,n)}i(a)},regexp:function(e,t,i,o,n){const a=[];if(e.required||!e.required&&o.hasOwnProperty(e.field)){if(Me(t)&&!e.required)return i();Je.required(e,t,o,a,n),Me(t)||Je.type(e,t,o,a,n)}i(a)},integer:function(e,t,i,o,n){const a=[];if(e.required||!e.required&&o.hasOwnProperty(e.field)){if(Me(t)&&!e.required)return i();Je.required(e,t,o,a,n),void 0!==t&&(Je.type(e,t,o,a,n),Je.range(e,t,o,a,n))}i(a)},float:function(e,t,i,o,n){const a=[];if(e.required||!e.required&&o.hasOwnProperty(e.field)){if(Me(t)&&!e.required)return i();Je.required(e,t,o,a,n),void 0!==t&&(Je.type(e,t,o,a,n),Je.range(e,t,o,a,n))}i(a)},array:function(e,t,i,o,n){const a=[];if(e.required||!e.required&&o.hasOwnProperty(e.field)){if(Me(t,"array")&&!e.required)return i();Je.required(e,t,o,a,n,"array"),Me(t,"array")||(Je.type(e,t,o,a,n),Je.range(e,t,o,a,n))}i(a)},object:function(e,t,i,o,n){const a=[];if(e.required||!e.required&&o.hasOwnProperty(e.field)){if(Me(t)&&!e.required)return i();Je.required(e,t,o,a,n),void 0!==t&&Je.type(e,t,o,a,n)}i(a)},enum:function(e,t,i,o,n){const a=[];if(e.required||!e.required&&o.hasOwnProperty(e.field)){if(Me(t)&&!e.required)return i();Je.required(e,t,o,a,n),void 0!==t&&Je.enum(e,t,o,a,n)}i(a)},pattern:function(e,t,i,o,n){const a=[];if(e.required||!e.required&&o.hasOwnProperty(e.field)){if(Me(t,"string")&&!e.required)return i();Je.required(e,t,o,a,n),Me(t,"string")||Je.pattern(e,t,o,a,n)}i(a)},date:function(e,t,i,o,n){const a=[];if(e.required||!e.required&&o.hasOwnProperty(e.field)){if(Me(t)&&!e.required)return i();if(Je.required(e,t,o,a,n),!Me(t)){let i;i="number"==typeof t?new Date(t):t,Je.type(e,i,o,a,n),i&&Je.range(e,i.getTime(),o,a,n)}}i(a)},url:Ge,hex:Ge,email:Ge,required:function(e,t,i,o,n){const a=[],s=Array.isArray(t)?"array":typeof t;Je.required(e,t,o,a,n,s),i(a)},any:function(e,t,i,o,n){const a=[];if(e.required||!e.required&&o.hasOwnProperty(e.field)){if(Me(t)&&!e.required)return i();Je.required(e,t,o,a,n)}i(a)}};function et(){return{default:"Validation error on field %s",required:"%s is required",enum:"%s must be one of %s",whitespace:"%s cannot be empty",date:{format:"%s date %s is invalid for format %s",parse:"%s date could not be parsed, %s is invalid ",invalid:"%s date %s is invalid"},types:{string:"%s is not a %s",method:"%s is not a %s (function)",array:"%s is not an %s",object:"%s is not an %s",number:"%s is not a %s",date:"%s is not a %s",boolean:"%s is not a %s",integer:"%s is not an %s",float:"%s is not a %s",regexp:"%s is not a valid %s",email:"%s is not a valid %s",url:"%s is not a valid %s",hex:"%s is not a valid %s"},string:{len:"%s must be exactly %s characters",min:"%s must be at least %s characters",max:"%s cannot be longer than %s characters",range:"%s must be between %s and %s characters"},number:{len:"%s must equal %s",min:"%s cannot be less than %s",max:"%s cannot be greater than %s",range:"%s must be between %s and %s"},array:{len:"%s must be exactly %s in length",min:"%s cannot be less than %s in length",max:"%s cannot be greater than %s in length",range:"%s must be between %s and %s in length"},pattern:{mismatch:"%s value %s does not match pattern %s"},clone:function(){const e=JSON.parse(JSON.stringify(this));return e.clone=this.clone,e}}}const tt=et();function it(e){this.rules=null,this._messages=tt,this.define(e)}it.prototype={messages:function(e){return e&&(this._messages=Ye(et(),e)),this._messages},define:function(e){if(!e)throw new Error("Cannot configure a schema with no rules");if("object"!=typeof e||Array.isArray(e))throw new Error("Rules must be an object");let t,i;for(t in this.rules={},e)e.hasOwnProperty(t)&&(i=e[t],this.rules[t]=Array.isArray(i)?i:[i])},validate:function(e,t,i){const o=this;void 0===t&&(t={}),void 0===i&&(i=function(){});let n,a,s=e,l=t,r=i;if("function"==typeof l&&(r=l,l={}),!this.rules||0===Object.keys(this.rules).length)return r&&r(),Promise.resolve();if(l.messages){let e=this.messages();e===tt&&(e=et()),Ye(e,l.messages),l.messages=e}else l.messages=this.messages();const u={};(l.keys||Object.keys(this.rules)).forEach((t=>{n=o.rules[t],a=s[t],n.forEach((i=>{let n=i;"function"==typeof n.transform&&(s===e&&(s={...s}),a=s[t]=n.transform(a)),n="function"==typeof n?{validator:n}:{...n},n.validator=o.getValidationMethod(n),n.field=t,n.fullField=n.fullField||t,n.type=o.getType(n),n.validator&&(u[t]=u[t]||[],u[t].push({rule:n,value:a,source:s,field:t}))}))}));const c={};return He(u,l,((e,t)=>{const{rule:i}=e;let o,n=!("object"!==i.type&&"array"!==i.type||"object"!=typeof i.fields&&"object"!=typeof i.defaultField);function a(e,t){return{...t,fullField:`${i.fullField}.${e}`}}function s(o){void 0===o&&(o=[]);let s=o;if(Array.isArray(s)||(s=[s]),!l.suppressWarning&&s.length&&it.warning("async-validator:",s),s.length&&i.message&&(s=[].concat(i.message)),s=s.map(We(i)),l.first&&s.length)return c[i.field]=1,t(s);if(n){if(i.required&&!e.value)return s=i.message?[].concat(i.message).map(We(i)):l.error?[l.error(i,Ue(l.messages.required,i.field))]:[],t(s);let o={};if(i.defaultField)for(const t in e.value)e.value.hasOwnProperty(t)&&(o[t]=i.defaultField);o={...o,...e.rule.fields};for(const e in o)if(o.hasOwnProperty(e)){const t=Array.isArray(o[e])?o[e]:[o[e]];o[e]=t.map(a.bind(null,e))}const n=new it(o);n.messages(l.messages),e.rule.options&&(e.rule.options.messages=l.messages,e.rule.options.error=l.error),n.validate(e.value,e.rule.options||l,(e=>{const i=[];s&&s.length&&i.push.apply(i,s),e&&e.length&&i.push.apply(i,e),t(i.length?i:null)}))}else t(s)}n=n&&(i.required||!i.required&&e.value),i.field=e.field,i.asyncValidator?o=i.asyncValidator(i,e.value,s,e.source,l):i.validator&&(o=i.validator(i,e.value,s,e.source,l),!0===o?s():!1===o?s(i.message||`${i.field} fails`):o instanceof Array?s(o):o instanceof Error&&s(o.message)),o&&o.then&&o.then((()=>s()),(e=>s(e)))}),(e=>{!function(e){let t,i=[],o={};function n(e){if(Array.isArray(e)){let t;i=(t=i).concat.apply(t,e)}else i.push(e)}for(t=0;t<e.length;t++)n(e[t]);i.length?o=Ee(i):(i=null,o=null),r(i,o)}(e)}))},getType:function(e){if(void 0===e.type&&e.pattern instanceof RegExp&&(e.type="pattern"),"function"!=typeof e.validator&&e.type&&!Qe.hasOwnProperty(e.type))throw new Error(Ue("Unknown rule type %s",e.type));return e.type||"string"},getValidationMethod:function(e){if("function"==typeof e.validator)return e.validator;const t=Object.keys(e),i=t.indexOf("message");return-1!==i&&t.splice(i,1),1===t.length&&"required"===t[0]?Qe.required:Qe[this.getType(e)]||!1}},it.register=function(e,t){if("function"!=typeof t)throw new Error("Cannot register a validator by type, validator is not a function");Qe[e]=t},it.warning=Ve,it.messages=tt,it.warning=function(){};const ot=he({name:"uv-form",mixins:[$,z,je],provide(){return{uForm:this}},data:()=>({formRules:{},validator:{},originalModel:null}),watch:{rules:{immediate:!0,handler(e){this.setRules(e)}},propsChange(e){var t;(null==(t=this.children)?void 0:t.length)&&this.children.map((e=>{"function"==typeof e.updateParentData&&e.updateParentData()}))},model:{immediate:!0,handler(e){this.originalModel||(this.originalModel=this.$uv.deepClone(e))}}},computed:{propsChange(){return[this.errorType,this.borderBottom,this.labelPosition,this.labelWidth,this.labelAlign,this.labelStyle]}},created(){this.children=[]},methods:{setRules(e){0!==Object.keys(e).length&&(this.formRules=e,this.validator=new it(e))},resetFields(){this.resetModel()},resetModel(e){this.children.map((e=>{const t=null==e?void 0:e.prop,i=this.$uv.getProperty(this.originalModel,t);this.$uv.setProperty(this.model,t,i)}))},clearValidate(e){e=[].concat(e),this.children.map((t=>{(void 0===e[0]||e.includes(t.prop))&&(t.message=null)}))},async validateField(e,t,i=null){this.$nextTick((()=>{const o=[];e=[].concat(e),this.children.map((t=>{const n=[];if(e.includes(t.prop)){const e=this.$uv.getProperty(this.model,t.prop),a=t.prop.split("."),s=a[a.length-1],l=this.formRules[t.prop];if(!l)return;const r=[].concat(l);for(let u=0;u<r.length;u++){const a=r[u],l=[].concat(null==a?void 0:a.trigger);if(i&&!l.includes(i))continue;new it({[s]:a}).validate({[s]:e},((e,i)=>{this.$uv.test.array(e)&&(o.push(...e),n.push(...e)),this.$nextTick((()=>{var e,i;t.message=(null==(e=n[0])?void 0:e.message)?null==(i=n[0])?void 0:i.message:null}))}))}}})),"function"==typeof t&&t(o)}))},validate(e){return new Promise(((e,t)=>{this.$nextTick((()=>{const i=this.children.map((e=>e.prop));this.validateField(i,(i=>{i.length?("toast"===this.errorType&&this.$uv.toast(i[0].message),t(i)):e(!0)}))}))}))}}},[["render",function(e,t,i,o,n,a){const s=j;return P(),I(s,{class:"uv-form"},{default:T((()=>[U(e.$slots,"default")])),_:3})}]]);const nt=he({name:"uv-gap",mixins:[$,z,{props:{bgColor:{type:String,default:"transparent"},height:{type:[String,Number],default:20},marginTop:{type:[String,Number],default:0},marginBottom:{type:[String,Number],default:0},...uni.$uvCommonProps,...null==(x=null==(w=uni.$uv)?void 0:w.props)?void 0:x.gap}}],computed:{gapStyle(){const e={backgroundColor:this.bgColor,height:this.$uv.addUnit(this.height),marginTop:this.$uv.addUnit(this.marginTop),marginBottom:this.$uv.addUnit(this.marginBottom)};return this.$uv.deepMerge(e,this.$uv.addStyle(this.customStyle))}}},[["render",function(e,t,i,o,n,a){const s=j;return P(),I(s,{class:"uv-gap",style:B([a.gapStyle])},null,8,["style"])}]]);const at=he({name:"uv-action-sheet",mixins:[Ne,Oe,$,z,{props:{title:{type:String,default:""},description:{type:String,default:""},actions:{type:Array,default:()=>[]},cancelText:{type:String,default:""},closeOnClickAction:{type:Boolean,default:!0},safeAreaInsetBottom:{type:Boolean,default:!0},openType:{type:String,default:""},closeOnClickOverlay:{type:Boolean,default:!0},round:{type:[Boolean,String,Number],default:0},...uni.$uvCommonProps,...null==(k=null==(C=uni.$uv)?void 0:C.props)?void 0:k.actionSheet}}],emits:["close","select"],computed:{itemStyle(){return e=>{let t={};return this.actions[e].color&&(t.color=this.actions[e].color),this.actions[e].fontSize&&(t.fontSize=this.$uv.addUnit(this.actions[e].fontSize)),this.actions[e].disabled&&(t.color="#c0c4cc"),t}}},methods:{open(){this.$refs.popup.open()},close(){this.$refs.popup.close()},popupChange(e){e.show||this.$emit("close")},cancel(){this.close()},selectHandler(e){const t=this.actions[e];!t||t.disabled||t.loading||(this.$emit("select",t),this.closeOnClickAction&&this.close())}}},[["render",function(e,t,i,o,n,a){const s=F,l=R(V("uv-icon"),ye),r=j,u=R(V("uv-line"),we),c=R(V("uv-loading-icon"),ke),d=R(V("uv-gap"),nt),p=R(V("uv-popup"),Ie);return P(),I(p,{ref:"popup",mode:"bottom",safeAreaInsetBottom:e.safeAreaInsetBottom,round:e.round,"close-on-click-overlay":e.closeOnClickOverlay,onChange:a.popupChange},{default:T((()=>[E(r,{class:"uv-action-sheet"},{default:T((()=>[e.title?(P(),I(r,{key:0,class:"uv-action-sheet__header"},{default:T((()=>[E(s,{class:"uv-action-sheet__header__title uv-line-1"},{default:T((()=>[q(A(e.title),1)])),_:1}),E(r,{class:"uv-action-sheet__header__icon-wrap",onClick:H(a.cancel,["stop"])},{default:T((()=>[E(l,{name:"close",size:"17",color:"#c8c9cc",bold:""})])),_:1},8,["onClick"])])),_:1})):O("",!0),e.description?(P(),I(s,{key:1,class:"uv-action-sheet__description",style:B([{marginTop:`${e.title&&e.description?0:"18px"}`}])},{default:T((()=>[q(A(e.description),1)])),_:1},8,["style"])):O("",!0),U(e.$slots,"default",{},(()=>[e.description?(P(),I(u,{key:0})):O("",!0),E(r,{class:"uv-action-sheet__item-wrap"},{default:T((()=>[(P(!0),X(K,null,Z(e.actions,((t,i)=>(P(),I(r,{key:i},{default:T((()=>[E(r,{class:"uv-action-sheet__item-wrap__item",onClick:H((e=>a.selectHandler(i)),["stop"]),"hover-class":t.disabled||t.loading?"":"uv-action-sheet--hover","hover-stay-time":150},{default:T((()=>[t.loading?(P(),I(c,{key:1,"custom-class":"van-action-sheet__loading",size:"18",mode:"circle"})):(P(),X(K,{key:0},[E(s,{class:"uv-action-sheet__item-wrap__item__name",style:B([a.itemStyle(i)])},{default:T((()=>[q(A(t.name),1)])),_:2},1032,["style"]),t.subname?(P(),I(s,{key:0,class:"uv-action-sheet__item-wrap__item__subname"},{default:T((()=>[q(A(t.subname),1)])),_:2},1024)):O("",!0)],64))])),_:2},1032,["onClick","hover-class"]),i!==e.actions.length-1?(P(),I(u,{key:0})):O("",!0)])),_:2},1024)))),128))])),_:1})]),!0),e.cancelText?(P(),I(d,{key:2,bgColor:"#eaeaec",height:"6"})):O("",!0),E(r,{"hover-class":"uv-action-sheet--hover"},{default:T((()=>[e.cancelText?(P(),I(s,{key:0,onTouchmove:t[0]||(t[0]=H((()=>{}),["stop","prevent"])),"hover-stay-time":150,class:"uv-action-sheet__cancel-text",onClick:a.cancel},{default:T((()=>[q(A(e.cancelText),1)])),_:1},8,["onClick"])):O("",!0)])),_:1})])),_:3})])),_:3},8,["safeAreaInsetBottom","round","close-on-click-overlay","onChange"])}],["__scopeId","data-v-047b4ace"]]),st=""+new URL("circle-header-6YpECDVM.png",import.meta.url).href,lt=""+new URL("header-star-B5VHPsxC.png",import.meta.url).href,rt=""+new URL("harf-circle-Qp-7f8RT.png",import.meta.url).href,ut=""+new URL("dot-6VoJ6dLH.png",import.meta.url).href,ct=he({__name:"home",setup(e){const{gProps:t,userStore:i,globalStore:o}=ce(),n=de([{name:"男",value:0},{name:"女",value:1}]),a=de([{name:"否",value:0},{name:"是",value:1}]);pe([]);const s=de({phoneNumber:"",code:"",password:"",name:"",rePassword:"",sex:"男"});async function l(){try{t.$toast("未到活动报名时间！")}catch(e){console.log(e),i.setToken("")}}return pe("发送验证码"),(e,t)=>{const i=N,o=F,r=j,u=R(V("uv-input"),ge),c=R(V("uv-form-item"),xe),d=R(V("uv-radio"),Ce),p=R(V("uv-upload"),Ae),h=R(V("uv-button"),Fe),f=R(V("uv-form"),ot),m=R(V("uv-action-sheet"),at),y=R(V("my-container"),fe);return P(),I(y,null,{default:T((()=>[E(r,{class:"content"},{default:T((()=>[E(i,{src:st,class:"circle-header"}),E(i,{src:lt,class:"header-star"}),E(i,{src:rt,class:"harf-circle"}),E(r,{class:"small-cart"},{default:T((()=>[E(r,{style:{"margin-top":"10rpx"}},{default:T((()=>[E(o,null,{default:T((()=>[q("AI驱动的自然资源数据创新与保护高级研修班")])),_:1})])),_:1})])),_:1}),E(r,{class:"title-card"},{default:T((()=>[E(o,null,{default:T((()=>[q("本期主题")])),_:1})])),_:1}),E(r,{class:"content-card"},{default:T((()=>[E(r,{class:"",style:{margin:"0 auto"}},{default:T((()=>[E(i,{src:ut,mode:"",style:{width:"29rpx",height:"29rpx","margin-right":"20rpx"}}),E(o,{style:{"font-size":"40rpx","font-weight":"600"}},{default:T((()=>[q("一、研修内容")])),_:1})])),_:1}),E(r,{class:"",style:{"margin-top":"20rpx"}},{default:T((()=>[E(o,null,{default:T((()=>[q("本期高级研修班以“AI驱动自然资源数据创新与保护”为主题，主要研修内容包括：人工智能技术、可信数据空间及自然资源领域的理论学习和实践操作，推动自然资源管理向科技化、信息化方向转型。通过系统化的课程设置，帮助学员掌握人工智能技术的基本原理及其在自然资源领域的应用场景；深入理解可信数据空间的构建方法，提升数据采集、处理与分析能力。同时，培养学员在跨学科、跨领域问题中的综合研判和决策能力，为自然资源管理领域输送一批具备创新思维和技术应用能力的专业人才。")])),_:1})])),_:1})])),_:1}),E(r,{class:"title-card",style:{"margin-top":"60rpx"}},{default:T((()=>[E(o,null,{default:T((()=>[q("活动内容")])),_:1})])),_:1}),E(r,{class:"content-card"},{default:T((()=>[E(r,{class:"",style:{margin:"0 auto","text-align":"center"}},{default:T((()=>[E(i,{src:ut,mode:"",style:{width:"29rpx",height:"29rpx","margin-right":"20rpx"}}),E(o,{style:{"font-size":"40rpx","font-weight":"600"}},{default:T((()=>[q("活动时间")])),_:1}),E(i,{src:ut,mode:"",style:{width:"29rpx",height:"29rpx","margin-left":"20rpx"}})])),_:1}),E(r,{class:"",style:{"margin-top":"30rpx","font-size":"36rpx","text-align":"center"}},{default:T((()=>[E(o,null,{default:T((()=>[q("2025年7月21日至25日，共5天。")])),_:1})])),_:1}),E(r,{class:"",style:{margin:"0 auto","text-align":"center","margin-top":"50rpx"}},{default:T((()=>[E(i,{src:ut,mode:"",style:{width:"29rpx",height:"29rpx","margin-right":"20rpx"}}),E(o,{style:{"font-size":"40rpx","font-weight":"600"}},{default:T((()=>[q("培训地点")])),_:1}),E(i,{src:ut,mode:"",style:{width:"29rpx",height:"29rpx","margin-left":"20rpx"}})])),_:1}),E(r,{class:"",style:{"margin-top":"30rpx","font-size":"36rpx","text-align":"center"}},{default:T((()=>[E(o,null,{default:T((()=>[q("昆明市西山区滇池路456号")])),_:1})])),_:1}),E(r,{class:"",style:{margin:"0 auto","text-align":"center","margin-top":"50rpx"}},{default:T((()=>[E(i,{src:ut,mode:"",style:{width:"29rpx",height:"29rpx","margin-right":"20rpx"}}),E(o,{style:{"font-size":"40rpx","font-weight":"600"}},{default:T((()=>[q("研修方式")])),_:1}),E(i,{src:ut,mode:"",style:{width:"29rpx",height:"29rpx","margin-left":"20rpx"}})])),_:1}),E(r,{class:"",style:{"margin-top":"30rpx","font-size":"36rpx","text-align":"center"}},{default:T((()=>[E(o,null,{default:T((()=>[q("本期高级研修班采取主题报告、专题研讨、学术交流等多种方式进行，研修班邀请知名行业专家、高校教授进行授课培训，同时结合研修主题，安排实地参观、现场教学。")])),_:1})])),_:1})])),_:1}),E(r,{class:"title-card",style:{"margin-top":"60rpx"}},{default:T((()=>[E(o,null,{default:T((()=>[q("在线报名")])),_:1})])),_:1}),E(r,{class:"content-card"},{default:T((()=>[E(f,{labelPosition:"left",model:e.model1,rules:e.rules,ref:"form",labelWidth:"80"},{default:T((()=>[E(c,{label:"姓名:",prop:"userInfo.name",borderBottom:""},{default:T((()=>[E(u,{modelValue:s.name,"onUpdate:modelValue":t[0]||(t[0]=e=>s.name=e),border:"none"},null,8,["modelValue"])])),_:1}),E(c,{label:"联系方式:",prop:"userInfo.name",borderBottom:""},{default:T((()=>[E(u,{modelValue:s.phoneNumber,"onUpdate:modelValue":t[1]||(t[1]=e=>s.phoneNumber=e),border:"none"},null,8,["modelValue"])])),_:1}),E(c,{label:"性别:",prop:"userInfo.sex",borderBottom:""},{default:T((()=>[(P(!0),X(K,null,Z(n,((e,t)=>(P(),I(d,{customStyle:{margin:"8px"},key:t,label:e.name,name:e.value},null,8,["label","name"])))),128))])),_:1}),E(c,{label:"是否住宿:",prop:"userInfo.sex",borderBottom:""},{default:T((()=>[(P(!0),X(K,null,Z(a,((e,t)=>(P(),I(d,{customStyle:{margin:"8px"},key:t,label:e.name,name:e.value},null,8,["label","name"])))),128))])),_:1}),E(c,{label:"是否用餐:",prop:"userInfo.sex",borderBottom:""},{default:T((()=>[(P(!0),X(K,null,Z(a,((e,t)=>(P(),I(d,{customStyle:{margin:"8px"},key:t,label:e.name,name:e.value},null,8,["label","name"])))),128))])),_:1}),E(c,{label:"上传附件:",prop:"userInfo.name",borderBottom:""},{default:T((()=>[E(p,{fileList:e.fileList1,name:"1",multiple:"",maxCount:10,onAfterRead:e.afterRead,onDelete:e.deletePic},null,8,["fileList","onAfterRead","onDelete"])])),_:1}),E(h,{type:"primary",text:"提交",customStyle:"margin-top: 10px;background-color:#87ceeb",onClick:l})])),_:1},8,["model","rules"]),E(m,{ref:"sexSelect",actions:e.actions,title:"请选择性别",description:"如果选择保密会报错",onSelect:e.sexSelect},null,8,["actions","onSelect"])])),_:1}),E(r,{style:{height:"80rpx"}})])),_:1}),E(r,{class:"author"},{default:T((()=>[q("昆明市土地开发整理中心 @版本号：V1.0.1")])),_:1})])),_:1})}}},[["__scopeId","data-v-e3e526db"]]);export{ct as default};
