<template>
	<fr-svg :width="size" :height="size" :src="iconSvg" />
</template>
<script>
	// #ifdef UNI-APP-X
	import icons from "../../mixins/icons.uts";
	// #endif
	// #ifndef UNI-APP-X
	import icons from "../../mixins/icons.js";
	// #endif
	export default {
		mixins: [icons],
		computed: {
			// #ifdef UNI-APP-X
			iconSvg(): string {
			// #endif
			// #ifndef UNI-APP-X
			iconSvg() {
			// #endif
				return `<?xml version="1.0" encoding="UTF-8"?><svg width="${this.size}" height="${this.size}" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M23.0013 13C15.7011 13 9.54283 18.0701 7.62207 25H38.3805C36.4597 18.0701 30.3014 13 23.0013 13Z" stroke="${this.colors(0)}" stroke-width="${this.strokeWidth}" stroke-linecap="${this.strokeLinecap}" stroke-linejoin="${this.strokeLinejoin}"/><path d="M7.00098 29.5926C7.00098 34.0112 8.66648 38.0265 11.382 41H34.62C37.3355 38.0265 39.001 34.0112 39.001 29.5926C39.001 27.9993 38.7844 26.4585 38.3802 25H7.62177C7.21753 26.4585 7.00098 27.9993 7.00098 29.5926Z" fill="${this.colors(1)}" stroke="${this.colors(0)}" stroke-width="${this.strokeWidth}" stroke-linecap="${this.strokeLinecap}" stroke-linejoin="${this.strokeLinejoin}"/><path d="M27 13V11C27 8.79086 25.2091 7 23 7V7C20.7909 7 19 8.79086 19 11V13" stroke="${this.colors(0)}" stroke-width="${this.strokeWidth}" stroke-linecap="${this.strokeLinecap}" stroke-linejoin="${this.strokeLinejoin}"/><path d="M7.0006 27.9999C7.0006 27.9999 5.01521 27.8685 4.00007 25.4999C2.5 21.9999 5.00023 19.9998 6.00025 16.9999C6.76104 14.7176 5.20688 13.014 4.41976 12.3296C4.16754 12.1103 4.00053 11.7992 4.00044 11.4649L4.00028 10.8473C4.00015 10.3583 4.35398 9.94412 4.84258 9.92617C5.87825 9.88814 7.66273 9.9969 9.00029 10.9999C11.0006 12.4999 12.0006 16.9999 12.0006 16.9999" stroke="${this.colors(0)}" stroke-width="${this.strokeWidth}" stroke-linecap="${this.strokeLinecap}" stroke-linejoin="${this.strokeLinejoin}"/><path d="M9 41L37 41" stroke="${this.colors(0)}" stroke-width="${this.strokeWidth}" stroke-linecap="${this.strokeLinecap}" stroke-linejoin="${this.strokeLinejoin}"/><path d="M39 25C41.7614 25 44 22.7614 44 20C44 17.2386 41.7614 15 39 15C36.9497 15 35.1876 16.2341 34.416 18" stroke="${this.colors(0)}" stroke-width="${this.strokeWidth}" stroke-linecap="${this.strokeLinecap}" stroke-linejoin="${this.strokeLinejoin}"/></svg>`
			}
		}
	}
</script>