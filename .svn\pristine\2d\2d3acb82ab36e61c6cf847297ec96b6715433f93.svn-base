## 2.0.1（2025-05-23）
1
## 2.0.0（2025-01-15）
代码拆分
## 1.2.0（2024-08-22）
1
## 1.1.9（2024-08-13）
1
## 1.1.8（2024-07-25）
优化
## 1.1.7（2024-06-22）
1
## 1.1.6（2024-06-21）
webSocket 优化
## 1.1.5（2024-06-21）
1
## 1.1.3（2024-06-11）
修改 webSocket 消息体属性名
## 1.1.2（2024-05-17）
demo 优化
## 1.1.1（2024-05-09）
demo修改
## 1.1.0（2024-04-26）
逻辑优化
## 1.0.19（2024-04-22）
文档更新
## 1.0.18（2024-04-03）
更新文档
## 1.0.17（2024-04-03）
添加请求失败也会调用响应拦截器,
示例增加请求loading统一处理
## 1.0.16（2024-04-03）
文档更新
## 1.0.15（2024-03-19）
文档修改
## 1.0.13（2024-03-18）
在响应结果里增加 reqConf 
## 1.0.12（2024-03-14）
1.增加上传文件限制处理
2.请求拦截器支持异步
## 1.0.11（2024-02-04）
文档修改
## 1.0.10（2024-01-17）
bug修复
## 1.0.9（2024-01-17）
更新文档
## 1.0.8（2024-01-17）
更新文档
## 1.0.7（2024-01-17）
更新文档
## 1.0.6（2024-01-16）
上传文件方法增加 uploadTask 对象和文件路径回调返回
## 1.0.3（2024-01-04）
修改请求拦截器
## 1.0.2（2024-01-03）
更新文档
## 1.0.1（2023-12-16）
修改上传方法入参
## 1.0.0（2023-12-16）
初版
