import{_ as e}from"./x-nav-bar.DkSVYhMv.js";import{M as a,N as l,O as s,r as o,h as r,c as t,w as d,o as c,j as n,_ as u,d as p,t as m,Y as f,W as h,X as i,i as _,f as b,I as w,g as y}from"./index-CYgRtUWM.js";import{_ as x}from"./x-placeholder.CpcxfQeX.js";import{_ as V,a as g}from"./my-container.CD5dlYbT.js";import"./com.NoCSJJtL.js";const N=V({__name:"forgotPwd",setup(V){const{gProps:N,userStore:v,globalStore:P}=a(),j=l({phoneNumber:"",code:"",password:"",rePassword:""});function $(){const e=N.hasEmptyField(j);if(e)return N.$toast({phoneNumber:"请填写手机号",code:"请填写验证码",password:"请填写密码",rePassword:"请填写确认密码"}[e]);if(j.password!=j.rePassword)return N.$toast("两次密码不一致");const{phoneNumber:a,code:l,password:s}=j;f({phoneNumber:a,code:l,password:s}).then((e=>{N.$toast("密码修改成功"),N.$navBack(500)}))}let U;const k=s("发送验证码");function S(){return U?N.$toast("请稍后再试"):j.phoneNumber?void h({phoneNumber:j.phoneNumber}).then((e=>{U=i(Date.now()+12e4,(({str:e},a)=>{k.value=e,a&&(U=null,k.value="发送验证码")}),"s S")})):N.$toast("请输入手机号")}return(a,l)=>{const s=o(r("x-nav-bar"),e),f=o(r("x-placeholder"),x),h=_,i=b,V=w,N=y,v=o(r("my-container"),g);return c(),t(v,null,{default:d((()=>[n(N,{class:"container f-c-x-c"},{default:d((()=>[n(s,{"bg-color":"transparent"}),n(f,{height:20}),n(h,{class:"logo",src:u,mode:""}),n(N,{class:"ipt"},{default:d((()=>[n(i,null,{default:d((()=>[p("手机号")])),_:1}),n(N,{class:"f-r-y-c"},{default:d((()=>[n(V,{modelValue:j.phoneNumber,"onUpdate:modelValue":l[0]||(l[0]=e=>j.phoneNumber=e),class:"f-1",type:"number",placeholder:"输入手机号","placeholder-class":"placeholder-class"},null,8,["modelValue"])])),_:1})])),_:1}),n(N,{class:"ipt",style:{"margin-bottom":"20rpx"}},{default:d((()=>[n(i,null,{default:d((()=>[p("验证码")])),_:1}),n(N,{class:"f-r-y-c"},{default:d((()=>[n(V,{modelValue:j.code,"onUpdate:modelValue":l[1]||(l[1]=e=>j.code=e),class:"f-1",type:"number",placeholder:"输入验证码","placeholder-class":"placeholder-class"},null,8,["modelValue"]),n(i,{style:{"font-size":"25rpx",color:"var(--theme-color)"},onClick:S},{default:d((()=>[p(m(k.value),1)])),_:1})])),_:1})])),_:1}),n(N,{class:"ipt",style:{"margin-bottom":"20rpx"}},{default:d((()=>[n(i,null,{default:d((()=>[p("密码")])),_:1}),n(N,{class:"f-r-y-c"},{default:d((()=>[n(V,{modelValue:j.password,"onUpdate:modelValue":l[2]||(l[2]=e=>j.password=e),class:"f-1",type:"password",placeholder:"输入密码","placeholder-class":"placeholder-class"},null,8,["modelValue"])])),_:1})])),_:1}),n(N,{class:"ipt",style:{"margin-bottom":"20rpx"}},{default:d((()=>[n(i,null,{default:d((()=>[p("确认密码")])),_:1}),n(N,{class:"f-r-y-c"},{default:d((()=>[n(V,{modelValue:j.rePassword,"onUpdate:modelValue":l[3]||(l[3]=e=>j.rePassword=e),class:"f-1",type:"password",placeholder:"再次输入密码","placeholder-class":"placeholder-class"},null,8,["modelValue"])])),_:1})])),_:1}),n(N,{class:"btn",style:{"margin-top":"100rpx"},onClick:$},{default:d((()=>[p(" 确认 ")])),_:1})])),_:1})])),_:1})}}},[["__scopeId","data-v-df0d3dea"]]);export{N as default};
