<template>
	<fr-svg :width="size" :height="size" :src="iconSvg" />
</template>
<script>
	// #ifdef UNI-APP-X
	import icons from "../../mixins/icons.uts";
	// #endif
	// #ifndef UNI-APP-X
	import icons from "../../mixins/icons.js";
	// #endif
	export default {
		mixins: [icons],
		computed: {
			// #ifdef UNI-APP-X
			iconSvg(): string {
			// #endif
			// #ifndef UNI-APP-X
			iconSvg() {
			// #endif
				return `<?xml version="1.0" encoding="UTF-8"?><svg width="${this.size}" height="${this.size}" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#icon-465529a3ffed8b0)"><circle cx="30.0746" cy="15.562" r="11" transform="rotate(40 30.0746 15.562)" fill="${this.colors(1)}" stroke="${this.colors(0)}" stroke-width="${this.strokeWidth}" stroke-linecap="${this.strokeLinecap}" stroke-linejoin="${this.strokeLinejoin}"/><path d="M21.6485 8.49117C17.0389 8.53954 15.322 5.1407 13.0722 7.82186C11.0395 10.2443 13.5652 13.4572 11.6369 15.7553" stroke="${this.colors(0)}" stroke-width="${this.strokeWidth}" stroke-linecap="${this.strokeLinecap}" stroke-linejoin="${this.strokeLinejoin}"/><path d="M25.3022 25.9167C25.3022 25.9167 21.1241 30.8959 19.5171 32.8111C17.9101 34.7262 17.8352 37.9269 15.9069 40.225C13.9785 42.5231 11.1609 42.7696 8.86272 40.8413C6.56459 38.9129 6.31808 36.0952 8.24644 33.7971C10.1748 31.499 13.3139 30.8694 14.9208 28.9543C16.5278 27.0392 20.0837 23.0608 20.7059 22.0599" stroke="${this.colors(0)}" stroke-width="${this.strokeWidth}" stroke-linecap="${this.strokeLinecap}" stroke-linejoin="${this.strokeLinejoin}"/><circle cx="11.2399" cy="19.3392" r="3" transform="rotate(40 11.2399 19.3392)" fill="${this.colors(1)}" stroke="${this.colors(0)}" stroke-width="${this.strokeWidth}" stroke-linecap="${this.strokeLinecap}" stroke-linejoin="${this.strokeLinejoin}"/><circle cx="28.4625" cy="37.7069" r="3" transform="rotate(40 28.4625 37.7069)" fill="${this.colors(1)}" stroke="${this.colors(0)}" stroke-width="${this.strokeWidth}" stroke-linecap="${this.strokeLinecap}" stroke-linejoin="${this.strokeLinejoin}"/><path d="M37.2158 24.165C37.8454 27.304 40.0686 32.4331 38.0786 34.0268C36.0886 35.6205 31.554 32.4682 30.3917 35.4091" stroke="${this.colors(0)}" stroke-width="${this.strokeWidth}" stroke-linecap="${this.strokeLinecap}" stroke-linejoin="${this.strokeLinejoin}"/></g><defs><clipPath id="icon-465529a3ffed8b0"><rect width="48" height="48" fill="${this.colors(2)}"/></clipPath></defs></svg>`
			}
		}
	}
</script>