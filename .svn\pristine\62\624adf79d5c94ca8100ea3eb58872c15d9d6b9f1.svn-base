import{r as e,j as t,o as i,c as s,a3 as o,w as n,g as a,l,b as r,n as c,q as d,z as h,L as u,M as p,k as m,e as f,E as g,N as y,d as x,v as k,x as v,J as _,F as b,f as w,a8 as C}from"./index-BGIsynos.js";import{_ as S}from"./x-nav-bar.CJscJIOu.js";import{i as $,_ as z}from"./icons.DIW3Pnk6.js";import{_ as V,a as j}from"./my-container.CIY3D98s.js";import{_ as I}from"./x-placeholder.bJd8l7m4.js";import{c as H,s as L}from"./com.lqmVd1lZ.js";const B=V({mixins:[$],computed:{iconSvg(){return`<?xml version="1.0" encoding="UTF-8"?><svg width="${this.size}" height="${this.size}" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M24 44C29.5228 44 34.5228 41.7614 38.1421 38.1421C41.7614 34.5228 44 29.5228 44 24C44 18.4772 41.7614 13.4772 38.1421 9.85786C34.5228 6.23858 29.5228 4 24 4C18.4772 4 13.4772 6.23858 9.85786 9.85786C6.23858 13.4772 4 18.4772 4 24C4 29.5228 6.23858 34.5228 9.85786 38.1421C13.4772 41.7614 18.4772 44 24 44Z" fill="${this.colors(1)}" stroke="${this.colors(0)}" stroke-width="${this.strokeWidth}" stroke-linejoin="${this.strokeLinejoin}"/><path d="M16 24L22 30L34 18" stroke="${this.colors(2)}" stroke-width="${this.strokeWidth}" stroke-linecap="${this.strokeLinecap}" stroke-linejoin="${this.strokeLinejoin}"/></svg>`}}},[["render",function(o,n,a,l,r,c){const d=e(t("fr-svg"),z);return i(),s(d,{width:o.size,height:o.size,src:c.iconSvg},null,8,["width","height","src"])}]]);const T=V({mixins:[$],computed:{iconSvg(){return`<?xml version="1.0" encoding="UTF-8"?><svg width="${this.size}" height="${this.size}" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="24" cy="24" r="20" fill="${this.colors(1)}" stroke="${this.colors(0)}" stroke-width="${this.strokeWidth}"/></svg>`}}},[["render",function(o,n,a,l,r,c){const d=e(t("fr-svg"),z);return i(),s(d,{width:o.size,height:o.size,src:c.iconSvg},null,8,["width","height","src"])}]]);const N=V({props:{modelValue:{type:Boolean,default:!1},size:{type:[String,Number],default:36},activeColor:{type:String,default:"#14aacd"},inactiveColor:{type:String,default:"#ccc"},disabled:{type:Boolean,default:!1}},emits:["update:modelValue","click","change"],watch:{modelValue(e){this.innerValue=e},innerValue(e){this.$emit("update:modelValue",e)}},data:()=>({innerValue:!1,systemInfo:o()}),created(){this.innerValue=this.modelValue},methods:{onClick(e){this.disabled||(this.innerValue=!this.innerValue,this.$emit("click",e),this.$emit("change",this.innerValue))}}},[["render",function(o,l,r,c,d,h){const u=e(t("check-one"),B),p=e(t("round"),T),m=a;return i(),s(m,{class:"x-radio",onClick:h.onClick},{default:n((()=>[d.innerValue?(i(),s(u,{key:0,theme:"filled",size:String(r.size),fill:r.activeColor},null,8,["size","fill"])):(i(),s(p,{key:1,theme:"outline",size:String(r.size),fill:r.inactiveColor},null,8,["size","fill"]))])),_:1},8,["onClick"])}],["__scopeId","data-v-7ab7c845"]]);const q=V({name:"my-button",props:{type:{type:String,default:"primary"},customClass:{type:String,default:""},width:{type:String,default:"690rpx"},height:{type:String,default:"88rpx"},radius:{type:String},openType:{},customStyle:{type:Object,default:()=>({})}},emits:["click","contact","getphonenumber"],computed:{innerStyle(){const{width:e,height:t,radius:i}=this;return{width:e,height:t,borderRadius:i||this.$upx2px(parseInt(t))/2+"px"}}},data:()=>({}),methods:{onClick(e){this.$emit("click",e)}}},[["render",function(e,t,o,a,u,p){const m=h;return i(),s(m,{class:r(`my-button f-r-xy-c ${o.type} ${o.customClass}`),style:c([p.innerStyle,o.customStyle]),"open-type":o.openType,onContact:t[0]||(t[0]=t=>e.$emit("contact",t)),onGetphonenumber:t[1]||(t[1]=t=>e.$emit("getphonenumber",t)),onClick:d(p.onClick,["stop"])},{default:n((()=>[l(e.$slots,"default",{},void 0,!0)])),_:3},8,["class","style","open-type","onClick"])}],["__scopeId","data-v-47c0eb2b"]]);const E=V({name:"x-fixed-footer",props:{width:{type:String,default:"1px"},height:{type:[Number,String],default:0},padding:{type:String,default:""},zIndex:{type:[Number,String],default:9},safeArea:{type:Boolean,default:!0},...H},emits:["height"],data:()=>({containerHeight:0,pagePath:u()}),computed:{innerStyle(){const{zIndex:e,padding:t,bgColor:i}=this;return{zIndex:e,padding:t,background:i}}},mounted(){this.setHeight()},updated(){this.setHeight()},methods:{str2px:L,queryElementRect:p,setHeight(){this.queryElementRect(".fixed-footer-container").then((e=>{this.pagePath==u()&&(this.containerHeight=e.height,this.$emit("height",this.containerHeight))}))}}},[["render",function(o,r,d,h,u,p){const g=e(t("x-placeholder"),I),y=a;return i(),s(y,{class:"x-fixed-footer",style:c({width:d.width,height:(p.str2px(d.height)||u.containerHeight)+"px"})},{default:n((()=>[m(y,{class:"fixed-footer-container",style:c([p.innerStyle,o.customStyle])},{default:n((()=>[l(o.$slots,"default",{},void 0,!0),d.safeArea?(i(),s(g,{key:0,"is-top":!1})):f("",!0)])),_:3},8,["style"])])),_:3},8,["style"])}],["__scopeId","data-v-9738219d"]]),F=V({__name:"setting",setup(o){const{globalProperties:l,userStore:r,globalStore:d}=g();return y((()=>{console.log("onLoad")})),(o,l)=>{const h=w,u=e(t("x-nav-bar"),S),p=a,f=e(t("x-radio"),N),g=e(t("my-button"),q),y=e(t("x-fixed-footer"),E),$=e(t("my-container"),j);return i(),s($,null,{default:n((()=>[m(p,{class:"container"},{default:n((()=>[m(u,null,{default:n((()=>[m(h,{class:"navbar_title"},{default:n((()=>[x("设置")])),_:1})])),_:1}),m(p,{class:"main"},{default:n((()=>[m(h,{class:"common_title"},{default:n((()=>[x("主题色")])),_:1}),m(p,{class:"theme f-r-y-c-sb"},{default:n((()=>[(i(!0),k(b,null,v(_(C),((e,t)=>(i(),s(p,{class:"item f-r-y-c",onClick:e=>_(d).setTheme(t)},{default:n((()=>[m(p,{class:"color",style:c({background:e["--theme-color"]})},null,8,["style"]),m(f,{"model-value":_(d).theme==t},null,8,["model-value"])])),_:2},1032,["onClick"])))),256))])),_:1})])),_:1}),m(y,{"custom-style":{background:"transparent",paddingBottom:"30rpx"}},{default:n((()=>[m(g,{type:"default",width:"300rpx",onClick:l[0]||(l[0]=e=>_(r).logout())},{default:n((()=>[x("退出登录")])),_:1})])),_:1})])),_:1})])),_:1})}}},[["__scopeId","data-v-91f39e2d"]]);export{F as default};
