import{_ as a}from"./x-placeholder.CpcxfQeX.js";import{M as s,N as e,r as l,h as o,c as t,w as r,o as c,j as n,_ as d,d as u,U as p,i as f,f as m,I as i,g as _}from"./index-CYgRtUWM.js";import{_ as g,a as h}from"./my-container.CD5dlYbT.js";import"./com.NoCSJJtL.js";const y=g({__name:"login",setup(g){const{gProps:y,userStore:b,globalStore:w}=s(),k=e({username:"",password:""});async function x(){try{if(y.hasEmptyField(k))return y.$toast("请填写完整");const a=await p(k);b.setToken(a),await b.setUserInfo(),y.$toast("登录成功"),y.$tabTo("/pagesMain/home",500)}catch(a){console.log(a),b.setToken("")}}return(s,e)=>{const p=l(o("x-placeholder"),a),g=f,y=m,b=i,w=_,V=l(o("my-container"),h);return c(),t(V,null,{default:r((()=>[n(w,{class:"container f-c-x-c"},{default:r((()=>[n(p,{height:44}),n(g,{class:"logo",src:d,mode:""}),n(w,{class:"ipt"},{default:r((()=>[n(y,null,{default:r((()=>[u("手机号")])),_:1}),n(w,{class:"f-r-y-c"},{default:r((()=>[n(b,{modelValue:k.username,"onUpdate:modelValue":e[0]||(e[0]=a=>k.username=a),class:"f-1",type:"number",placeholder:"输入手机号","placeholder-class":"placeholder-class"},null,8,["modelValue"])])),_:1})])),_:1}),n(w,{class:"ipt",style:{"margin-bottom":"20rpx"}},{default:r((()=>[n(y,null,{default:r((()=>[u("密码")])),_:1}),n(w,{class:"f-r-y-c"},{default:r((()=>[n(b,{modelValue:k.password,"onUpdate:modelValue":e[1]||(e[1]=a=>k.password=a),class:"f-1",type:"password",placeholder:"输入密码","placeholder-class":"placeholder-class"},null,8,["modelValue"])])),_:1})])),_:1}),n(w,{class:"forgot",onClick:e[2]||(e[2]=a=>s.$navTo("/pages/forgotPwd"))},{default:r((()=>[u(" 忘记密码？ ")])),_:1}),n(w,{class:"btn",onClick:x},{default:r((()=>[u(" 登录 ")])),_:1}),n(w,{class:"btn",style:{"background-color":"transparent",color:"var(--theme-color)"},onClick:e[3]||(e[3]=a=>s.$redTo("/pages/register"))},{default:r((()=>[u(" 注册 ")])),_:1})])),_:1})])),_:1})}}},[["__scopeId","data-v-1aaef461"]]);export{y as default};
