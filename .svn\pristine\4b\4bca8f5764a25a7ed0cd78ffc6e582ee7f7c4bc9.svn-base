<template>
	<fr-svg :width="size" :height="size" :src="iconSvg" />
</template>
<script>
	// #ifdef UNI-APP-X
	import icons from "../../mixins/icons.uts";
	// #endif
	// #ifndef UNI-APP-X
	import icons from "../../mixins/icons.js";
	// #endif
	export default {
		mixins: [icons],
		computed: {
			// #ifdef UNI-APP-X
			iconSvg(): string {
			// #endif
			// #ifndef UNI-APP-X
			iconSvg() {
			// #endif
				return `<?xml version="1.0" encoding="UTF-8"?><svg width="${this.size}" height="${this.size}" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M14.5397 20.0187C12.8522 17.9435 11.2675 17.498 9.78564 18.6822C7.5629 20.4584 6.92453 26.6497 8.71324 32.1087C10.502 37.5678 13.9801 45.0018 21.0016 45.0018C28.0231 45.0018 29.684 37.5223 32.5485 33.0002C35.413 28.4781 36.9285 24.1153 34.1208 18.6822" stroke="${this.colors(0)}" stroke-width="${this.strokeWidth}" stroke-linecap="${this.strokeLinecap}" stroke-linejoin="${this.strokeLinejoin}"/><path d="M11 18.037C9.29707 15.4429 7.96374 13.4307 6.99996 12.0003C5.5543 9.85474 9.25107 7.08175 11 8.96818C12.1659 10.2258 13.7148 12.0781 15.6466 14.5251" stroke="${this.colors(0)}" stroke-width="${this.strokeWidth}" stroke-linecap="${this.strokeLinecap}"/><path d="M15.0236 25.6397C14.5391 19.576 14.9333 15.6277 16.2062 13.7948C18.1155 11.0456 21.6631 10.0032 25.0035 10.0032C26.9924 10.0032 28.8087 10.8503 30.4525 12.5445" stroke="${this.colors(0)}" stroke-width="${this.strokeWidth}" stroke-linecap="${this.strokeLinecap}" stroke-linejoin="${this.strokeLinejoin}"/><path fill-rule="evenodd" clip-rule="evenodd" d="M41.0003 12.6129C41.5858 14.6493 40.6294 16.5098 37.6844 16.9311C34.7393 17.3525 32.5313 18.8334 30.9388 20.0792C29.3463 21.3249 26.4983 25.1047 25.9361 27.0024C25.3738 28.9001 22.1602 27.1548 21.2971 26.3972C20.434 25.6395 19.5855 23.9807 21.2971 22.2458C23.0086 20.5109 22.6383 20.1647 22.6383 18.4053C22.6383 16.646 32.0003 10.8264 37.2729 10.2942C38.4449 10.2258 40.4147 10.5765 41.0003 12.6129Z" stroke="${this.colors(0)}" stroke-width="${this.strokeWidth}"/><path d="M23.0078 4.00024V9.26294" stroke="${this.colors(0)}" stroke-width="${this.strokeWidth}" stroke-linecap="${this.strokeLinecap}"/><path d="M20.3066 10.7179C17.2888 6.92544 14.8555 4.80879 13.0068 4.36792" stroke="${this.colors(0)}" stroke-width="${this.strokeWidth}" stroke-linecap="${this.strokeLinecap}"/><path d="M17.0039 7.02904L17.9944 2.96167" stroke="${this.colors(0)}" stroke-width="${this.strokeWidth}" stroke-linecap="${this.strokeLinecap}"/><path d="M35.6128 10.7175C35.2905 11.822 35.2905 12.8576 35.6128 13.8242C35.935 14.7909 36.6255 15.8265 37.6842 16.931" stroke="${this.colors(0)}" stroke-width="${this.strokeWidth}" stroke-linecap="${this.strokeLinecap}"/></svg>`
			}
		}
	}
</script>