<template>
	<fr-svg :width="size" :height="size" :src="iconSvg" />
</template>
<script>
	// #ifdef UNI-APP-X
	import icons from "../../mixins/icons.uts";
	// #endif
	// #ifndef UNI-APP-X
	import icons from "../../mixins/icons.js";
	// #endif
	export default {
		mixins: [icons],
		computed: {
			// #ifdef UNI-APP-X
			iconSvg(): string {
			// #endif
			// #ifndef UNI-APP-X
			iconSvg() {
			// #endif
				return `<?xml version="1.0" encoding="UTF-8"?><svg width="${this.size}" height="${this.size}" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M22 14.0001C22 14.0001 19.2998 19.2934 18 26.0001C16.7001 32.7068 17 42.0001 17 42.0001" stroke="${this.colors(0)}" stroke-width="${this.strokeWidth}" stroke-linecap="${this.strokeLinecap}" stroke-linejoin="${this.strokeLinejoin}"/><path d="M33.953 23.2722C34.2986 23.5017 34.8456 23.6634 35.3814 23.7751C36.3128 23.9692 37.1735 23.329 37.1492 22.3779C37.1041 20.6042 36.4122 17.7028 32.8908 15.3644C29.5662 13.1566 26.2646 13.1264 24.1832 13.4437C22.996 13.6247 22.5233 14.9219 23.2046 15.9109C23.8127 16.7937 24.5211 17.6853 25 17.8556C26 18.2111 27.2034 17.2737 28.0797 17.8556C28.956 18.4375 28.6953 19.7807 29.5716 20.3626C30.4479 20.9445 31.5848 20.1832 32.4611 20.7651C33.3373 21.3471 33.0767 22.6903 33.953 23.2722Z" fill="${this.colors(1)}" stroke="${this.colors(0)}" stroke-width="${this.strokeWidth}" stroke-linecap="${this.strokeLinecap}" stroke-linejoin="${this.strokeLinejoin}"/><path d="M20.0002 16.9999C20.858 16.714 21.3888 15.7739 21.686 15.0214C21.9317 14.3995 21.7122 13.713 21.1366 13.3727C19.8409 12.6068 17.0755 11.5589 12.7616 12.8117C8.49748 14.05 7.37106 16.8675 7.08518 18.5267C6.95141 19.3031 7.48001 20.0054 8.263 20.0924C8.8239 20.1548 9.43921 20.1271 9.8069 19.815C10.6138 19.1302 10.8323 18.2326 11.7338 17.991C12.6353 17.7495 13.413 18.8488 14.3146 18.6072C15.2161 18.3657 15.3399 17.0248 16.2414 16.7832C17.143 16.5416 18.5002 17.4999 20.0002 16.9999Z" fill="${this.colors(1)}" stroke="${this.colors(0)}" stroke-width="${this.strokeWidth}" stroke-linecap="${this.strokeLinecap}" stroke-linejoin="${this.strokeLinejoin}"/><path d="M27 6.00012C24.4995 7.00013 21.9993 12.0001 22.0001 14.0001L35.0009 8.00014C33.6202 5.60868 29.5005 5.00012 27 6.00012Z" fill="${this.colors(1)}" stroke="${this.colors(0)}" stroke-width="${this.strokeWidth}" stroke-linecap="${this.strokeLinecap}" stroke-linejoin="${this.strokeLinejoin}"/><path d="M20 5.00012C24 6.42174 23.3809 11.6087 22.0002 14.0001L10.0005 5.92177C11.0005 4.00013 16 3.5785 20 5.00012Z" fill="${this.colors(1)}" stroke="${this.colors(0)}" stroke-width="${this.strokeWidth}" stroke-linecap="${this.strokeLinecap}" stroke-linejoin="${this.strokeLinejoin}"/><path d="M26 35C34.2843 35 39 36.7909 39 39C39 41.2091 32.2843 43 24 43C15.7157 43 9 41.2091 9 39C9 38.4608 9.40006 37.9465 10.1253 37.4771" stroke="${this.colors(0)}" stroke-width="${this.strokeWidth}" stroke-linecap="${this.strokeLinecap}"/></svg>`
			}
		}
	}
</script>