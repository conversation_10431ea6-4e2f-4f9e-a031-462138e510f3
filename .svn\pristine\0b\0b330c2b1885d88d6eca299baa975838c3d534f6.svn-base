<template>
	<fr-svg :width="size" :height="size" :src="iconSvg" />
</template>
<script>
	// #ifdef UNI-APP-X
	import icons from "../../mixins/icons.uts";
	// #endif
	// #ifndef UNI-APP-X
	import icons from "../../mixins/icons.js";
	// #endif
	export default {
		mixins: [icons],
		computed: {
			// #ifdef UNI-APP-X
			iconSvg(): string {
			// #endif
			// #ifndef UNI-APP-X
			iconSvg() {
			// #endif
				return `<?xml version="1.0" encoding="UTF-8"?><svg width="${this.size}" height="${this.size}" viewBox="0 0 48 48" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M44 40V20C44 18.3431 42.6569 17 41 17H35.715C34.1737 17 32.8226 18.2428 31.7911 19.388C30.6326 20.6742 28.3769 22 24 22C19.6231 22 17.3674 20.6742 16.2089 19.388C15.1774 18.2428 13.8263 17 12.285 17H7C5.34315 17 4 18.3431 4 20V40C4 41.6569 5.34315 43 7 43H41C42.6569 43 44 41.6569 44 40Z" fill="${this.colors(1)}" stroke="${this.colors(0)}" stroke-width="${this.strokeWidth}" stroke-linejoin="${this.strokeLinejoin}"/><path d="M33 17C33 19.7614 28.9706 22 24 22C19.0294 22 15 19.7614 15 17" stroke="${this.colors(0)}" stroke-width="${this.strokeWidth}" stroke-linecap="${this.strokeLinecap}" stroke-linejoin="${this.strokeLinejoin}"/><ellipse cx="24" cy="10" rx="9" ry="5" stroke="${this.colors(0)}" stroke-width="${this.strokeWidth}" stroke-linecap="${this.strokeLinecap}" stroke-linejoin="${this.strokeLinejoin}"/><path d="M33 17V10" stroke="${this.colors(0)}" stroke-width="${this.strokeWidth}" stroke-linecap="${this.strokeLinecap}" stroke-linejoin="${this.strokeLinejoin}"/><path d="M15 17V10" stroke="${this.colors(0)}" stroke-width="${this.strokeWidth}" stroke-linecap="${this.strokeLinecap}" stroke-linejoin="${this.strokeLinejoin}"/><ellipse cx="27" cy="10" rx="2" ry="1" fill="${this.colors(0)}"/><ellipse cx="21" cy="10" rx="2" ry="1" fill="${this.colors(0)}"/></svg>`
			}
		}
	}
</script>